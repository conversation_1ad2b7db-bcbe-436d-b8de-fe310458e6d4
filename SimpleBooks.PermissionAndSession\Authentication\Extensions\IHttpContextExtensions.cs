﻿namespace SimpleBooks.PermissionAndSession.Authentication.Extensions
{
    public static class IHttpContextExtensions
    {
        public static Ulid? GetEmployeeId(this HttpContext httpContext)
        {
            Ulid? employeeId = null;
            var empIdClaim = httpContext.User.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.NameId);
            if (empIdClaim != null && Ulid.TryParse(empIdClaim.Value, out var parsedEmpId))
                employeeId = parsedEmpId;
            return employeeId;
        }

        public static string GetTokenString(this HttpContext httpContext)
        {
            string tokenString = string.Empty;
            if (httpContext.Request.Headers.TryGetValue(AuthenticationDefaults.AuthenticationHeader, out var headersAuthToken) && !string.IsNullOrEmpty(headersAuthToken))
                tokenString = headersAuthToken.ToString();
            else if (httpContext.User?.Identity?.IsAuthenticated is bool isAuthenticated && isAuthenticated)
                tokenString = httpContext.User.Claims.FirstOrDefault(c => c.Type == "access_token")?.Value ?? string.Empty;
            return tokenString;
        }
    }
}
