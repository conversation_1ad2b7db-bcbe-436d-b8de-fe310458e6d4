﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckCollectionService : SimpleBooksBaseService<CheckCollectionModel, CheckCollectionModel, CreateCheckCollectionViewModel, UpdateCheckCollectionViewModel>, ICheckCollectionService
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckCollectionService(

            IHttpClientFactory httpClientFactory,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(httpClientFactory)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers()
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Deposited.Value);
                if (result == null)
                    return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure("Failed to retrieve check treasury vouchers.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure($"An error occurred while retrieving check treasury vouchers: {ex.Message}");
            }
        }
    }
}
