﻿namespace SimpleBooks.Services.API.Business.HR
{
    public class EmployeeService : SimpleBooksBaseService<EmployeeModel, EmployeeModel, CreateEmployeeViewModel, UpdateEmployeeViewModel>, IEmployeeService
    {
        public EmployeeService(IHttpClientFactory httpClientFactory) : base(httpClientFactory)
        {
        }


        [HttpRequestMethod(nameof(GetRepEmployeeSelectListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<IdAndName>>> GetRepEmployeeSelectListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<IdAndName>>>(this) ?? new List<IdAndName>();
            return result;
        }
    }
}
