﻿namespace SimpleBooks.Services.API.Business.Sales
{
    public class SalesOrderService : SimpleBooksBaseService<SalesOrderModel, IndexSalesOrderViewModel, CreateSalesOrderViewModel, UpdateSalesOrderViewModel>, ISalesOrderService
    {
        private readonly ICustomerService _customerService;
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public SalesOrderService(

            IHttpClientFactory httpClientFactory,
            ICustomerService customerService,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(httpClientFactory)
        {
            _customerService = customerService;
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync() => await _customerService.SelectiveCustomerDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();

        [HttpRequestMethod(nameof(GetInvoicedQuantitiesAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<Dictionary<string, decimal>>> GetInvoicedQuantitiesAsync(Ulid salesOrderId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<Dictionary<string, decimal>>>(this, new { salesOrderId });
                if (result == null)
                    return ServiceResult<Dictionary<string, decimal>>.Failure("No data found or request failed.");
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod(nameof(GetInvoiceDetailsByOrderAndProductAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<InvoiceDetailsDto>>> GetInvoiceDetailsByOrderAndProductAsync(Ulid salesOrderId, Ulid productId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<InvoiceDetailsDto>>>(this, new { salesOrderId, productId });
                if (result == null)
                    return ServiceResult<IEnumerable<InvoiceDetailsDto>>.Failure("No data found or request failed.");
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
