﻿namespace GMCadiomCore.Models.Model
{
    public class BaseIdentityModel : IBaseIdentityModel, INotifyPropertyChanged
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Ulid Id { get; set; } = Ulid.NewUlid();

        protected bool CheckPropertyChanged<T>(ref T oldValue, ref T newValue, [CallerMemberName] string propertyName = "")
        {
            if (oldValue == null && newValue == null)
                return false;

            if (oldValue == null && newValue != null || oldValue != null && !oldValue.Equals(newValue))
            {
                oldValue = newValue;
                FirePropertyChanged(propertyName);
                return true;
            }

            return false;
        }

        protected void FirePropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
        }

        public event PropertyChangedEventHandler? PropertyChanged;
    }
}
