﻿namespace SimpleBooks.PermissionAndSession.Authentication.Extensions
{
    public static class IServiceProviderExtensions
    {
        public static Ulid? GetEmployeeId(this IServiceProvider serviceProvider)
        {
            Ulid? employeeId = null;
            var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
                employeeId = httpContext.GetEmployeeId();
            return employeeId;
        }

        public static string GetTokenString(this IServiceProvider serviceProvider)
        {
            string tokenString = string.Empty;
            var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
                tokenString = httpContext.GetTokenString();
            return tokenString;
        }
    }
}
