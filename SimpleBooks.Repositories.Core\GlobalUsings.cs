﻿global using GMCadiomCore.Models.Abstractions;
global using GMCadiomCore.Models.Model;
global using GMCadiomCore.Repositories.IFactory;
global using GMCadiomCore.Repositories.IRepository;
global using SimpleBooks.Models.Model;
global using SimpleBooks.Models.Model.HR;
global using SimpleBooks.Models.Model.Purchases;
global using SimpleBooks.Models.Model.Sales;
global using SimpleBooks.Models.Model.Tax;
global using SimpleBooks.Models.Model.Treasury;
global using SimpleBooks.Models.Model.Treasury.BankManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.Model.Treasury.CashManagement;
global using SimpleBooks.Models.Model.User;
global using SimpleBooks.Models.Model.Warehouse;
global using SimpleBooks.Models.ModelDTO.Authentication;
global using SimpleBooks.Models.ModelDTO.Purchases;
global using SimpleBooks.Models.ModelDTO.Sales;
global using SimpleBooks.Models.ModelDTO.Tax;
global using SimpleBooks.Models.ViewModel.Purchases.Bill;
global using SimpleBooks.Models.ViewModel.Purchases.BillReturn;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder;
global using SimpleBooks.Models.ViewModel.Purchases.Vendor;
global using SimpleBooks.Models.ViewModel.Sales.Customer;
global using SimpleBooks.Models.ViewModel.Sales.Invoice;
global using SimpleBooks.Models.ViewModel.Sales.InvoiceReturn;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrder;
global using SimpleBooks.Models.ViewModel.Warehouse.Product;
global using SimpleBooks.Repositories.Core.IRepository.Purchases;
global using SimpleBooks.Repositories.Core.IRepository.Sales;
global using SimpleBooks.Repositories.Core.IRepository.Tax;
global using SimpleBooks.Repositories.Core.IRepository.Treasury;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.CashManagement;
global using SimpleBooks.Repositories.Core.IRepository.User;
