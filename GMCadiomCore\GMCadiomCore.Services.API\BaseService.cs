﻿namespace GMCadiomCore.Services.API
{
    [HttpRequestController]
    public class BaseService<TEntity, TEntityView> : IBaseService<TEntity, TEntityView> where TEntity : class, IBaseIdentityModel where TEntityView : class, IBaseIdentityModel
    {
        protected HttpClient _httpClient;

        public BaseService(HttpClient? httpClient = null)
        {
            _httpClient = httpClient ?? new HttpClient();
        }

        public virtual void EditModelBeforeSave(TEntity model)
        {
        }

        public virtual void ValidateEntity(TEntity model, bool isEdit)
        {
            new BaseValidation().Validate(model);
        }

        public virtual TEntity SetEntity(TEntity model, TEntity entity)
        {
            if (model == null || entity == null)
                throw new ArgumentNullException("Model and entity cannot be null");

            var properties = typeof(TEntity).GetProperties();

            foreach (var property in properties)
            {
                if (property.CanRead && property.CanWrite)
                {
                    var value = property.GetValue(model);
                    property.SetValue(entity, value);
                }
            }

            return entity;
        }

        #region Async
        [HttpRequestMethod("GetAllAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<IEnumerable<TEntity>>> GetAllAsync()
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TEntity>>>(this);
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetAllPaginationListAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<PaginationList<TEntity>>> GetAllPaginationListAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaginationList<TEntity>>>(this, new { pageNumber, pageSize });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetAllByNameAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<PaginationList<TEntity>>> GetAllByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaginationList<TEntity>>>(this, new { name, pageNumber, pageSize });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetAllViewAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaginationList<TEntityView>>>(this, new { pageNumber, pageSize });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetAllViewByNameAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                PaginationList<TEntityView> result = await _httpClient.SendRequest<PaginationList<TEntityView>>(this, new { name, pageNumber, pageSize }) ?? new PaginationList<TEntityView>(new List<TEntityView>(), 0, pageNumber, pageSize);
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetAllViewByAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewByAsync(TEntityView searchValues, int pageNumber, int pageSize = 25)
        {
            try
            {
                string searchValuesJson = JsonConvert.SerializeObject(searchValues, Formatting.Indented);
                var result = await _httpClient.SendRequest<ServiceResult<PaginationList<TEntityView>>>(this, new { searchValuesJson, pageNumber, pageSize });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("GetSelectListAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<IEnumerable<IdAndName>>> GetSelectListAsync(Expression<Func<TEntity, bool>>? searchValue = null)
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<IdAndName>>>(this, searchValue);
            return result;
        }

        [HttpRequestMethod("GetByIdAsync", HttpMethodEnum.GET)]
        public virtual async Task<ServiceResult<TEntity?>> GetByIdAsync(Ulid id)
        {
            var result = await _httpClient.SendRequest<ServiceResult<TEntity?>>(this, new { id });
            return result;
        }

        [HttpRequestMethod("IsExistAsync", HttpMethodEnum.GET)]
        public async Task<ServiceResult<bool>> IsExistAsync(Ulid id)
        {
            var result = await _httpClient.SendRequest<ServiceResult<bool>>(this, new { id });
            return result;
        }

        [HttpRequestMethod("AddAsync", HttpMethodEnum.POST, true)]
        public virtual async Task<ServiceResult<TEntity?>> AddAsync(TEntity model)
        {
            try
            {
                EditModelBeforeSave(model);
                ValidateEntity(model, false);
                var result = await _httpClient.SendRequest<ServiceResult<TEntity?>>(this, model);
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("UpdateAsync", HttpMethodEnum.PUT, true)]
        public virtual async Task<ServiceResult<TEntity?>> UpdateAsync(TEntity model)
        {
            try
            {
                var entity = GetById(model.Id).Data;

                if (entity is null)
                    throw new NullReferenceException("Entity not found");

                entity = SetEntity(model, entity);

                EditModelBeforeSave(entity);
                ValidateEntity(entity, true);
                var result = await _httpClient.SendRequest<ServiceResult<TEntity?>>(this, model);
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod("RemoveAsync", HttpMethodEnum.DELETE)]
        public virtual async Task<ServiceResult<bool>> RemoveAsync(Ulid id)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<bool>>(this, new { id });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        #endregion

        #region Sync
        [HttpRequestMethod("GetAll", HttpMethodEnum.GET)]
        public ServiceResult<IEnumerable<TEntity>> GetAll() => Task.Run(async () => await GetAllAsync()).GetAwaiter().GetResult();

        [HttpRequestMethod("GetAllPaginationList", HttpMethodEnum.GET)]
        public ServiceResult<PaginationList<TEntity>> GetAllPaginationList(int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllPaginationListAsync(pageNumber, pageSize)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetAllByName", HttpMethodEnum.GET)]
        public ServiceResult<PaginationList<TEntity>> GetAllByName(string name, int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllByNameAsync(name, pageNumber, pageSize)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetAllView", HttpMethodEnum.GET)]
        public ServiceResult<PaginationList<TEntityView>> GetAllView(int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllViewAsync(pageNumber, pageSize)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetAllViewByName", HttpMethodEnum.GET)]
        public ServiceResult<PaginationList<TEntityView>> GetAllViewByName(string name, int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllViewByNameAsync(name, pageNumber, pageSize)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetAllViewBy", HttpMethodEnum.GET)]
        public ServiceResult<PaginationList<TEntityView>> GetAllViewBy(TEntityView searchValues, int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllViewByAsync(searchValues, pageNumber, pageSize)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetSelectList", HttpMethodEnum.GET)]
        public ServiceResult<IEnumerable<IdAndName>> GetSelectList(Expression<Func<TEntity, bool>>? searchValue = null) => Task.Run(async () => await GetSelectListAsync(searchValue)).GetAwaiter().GetResult();

        [HttpRequestMethod("GetById", HttpMethodEnum.GET)]
        public ServiceResult<TEntity?> GetById(Ulid id) => Task.Run(async () => await GetByIdAsync(id)).GetAwaiter().GetResult();

        [HttpRequestMethod("IsExist", HttpMethodEnum.GET)]
        public ServiceResult<bool> IsExist(Ulid id) => Task.Run(async () => await IsExistAsync(id)).GetAwaiter().GetResult();

        [HttpRequestMethod("Add", HttpMethodEnum.POST, true)]
        public ServiceResult<TEntity?> Add(TEntity model) => Task.Run(async () => await AddAsync(model)).GetAwaiter().GetResult();

        [HttpRequestMethod("Update", HttpMethodEnum.PUT, true)]
        public ServiceResult<TEntity?> Update(TEntity model) => Task.Run(async () => await UpdateAsync(model)).GetAwaiter().GetResult();

        [HttpRequestMethod("Remove", HttpMethodEnum.DELETE)]
        public ServiceResult<bool> Remove(Ulid id) => Task.Run(async () => await RemoveAsync(id)).GetAwaiter().GetResult();
        #endregion
    }
}
