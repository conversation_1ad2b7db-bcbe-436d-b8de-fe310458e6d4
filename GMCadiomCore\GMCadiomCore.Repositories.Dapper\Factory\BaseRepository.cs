﻿namespace GMCadiomCore.Repositories.Dapper.Factory
{
    public class BaseRepository<TEntity, TEntityView> : IBaseRepository<TEntity, TEntityView> where TEntity : BaseIdentityModel where TEntityView : BaseIdentityModel
    {
        protected readonly IDbConnection _dbConnection;
        public BaseRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        #region Async
        public virtual async Task<TEntity?> AddAsync(TEntity? entity)
        {
            if (entity != null)
                using (IDbConnection db = _dbConnection)
                {
                    string insertQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Insert);

                    var result = await db.ExecuteAsync(insertQuery, DapperQueryBuilder.CreateDynamicParameters<TEntity>(entity));
                    DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                    {
                        Entity = entity,
                        EntityState = DataBaseEntityState.Added,
                        EntityType = typeof(TEntity).Name,
                    });
                }
            return entity;
        }

        public virtual async Task<TEntity?> UpdateAsync(TEntity? entity)
        {
            if (entity != null)
                using (IDbConnection db = _dbConnection)
                {
                    string updateQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Edit);

                    var result = await db.ExecuteAsync(updateQuery, DapperQueryBuilder.CreateDynamicParameters<TEntity>(entity));
                    DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                    {
                        Entity = entity,
                        EntityState = DataBaseEntityState.Modified,
                        EntityType = typeof(TEntity).Name,
                    });
                }
            return entity;
        }

        public virtual async Task<TEntity?> RemoveAsync(TEntity? entity)
        {
            if (entity != null)
                using (IDbConnection db = _dbConnection)
                {
                    //IDeleteValidation validate = new DeleteValidation(UnitOfWork.GetInstace(), Model);

                    string deleteQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Delete);

                    var result = await db.ExecuteAsync(deleteQuery, DapperQueryBuilder.CreateDynamicParameters<TEntity>(entity));
                    DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                    {
                        Entity = entity,
                        EntityState = DataBaseEntityState.Deleted,
                        EntityType = typeof(TEntity).Name,
                    });
                }
            return entity;
        }

        public virtual async Task<TEntity?> GetAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Select);

                var result = await db.QueryAsync<TEntity>(selectQuery);

                if (repositorySpecifications != null)
                    if (repositorySpecifications.SearchValue != null)
                        result = result.AsQueryable().Where(repositorySpecifications.SearchValue);

                TEntity? entity = result.FirstOrDefault();
                return entity;
            }
        }

        public virtual async Task<bool> IsExistAsync(Ulid id)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Exist);

                var result = await db.ExecuteScalarAsync<bool>(selectQuery, new { Id = id });

                return result;
            }
        }

        public virtual async Task<PaginationList<TEntity>> GetAllPaginationListAsync(PaginationSpecifications<TEntity> paginationSpecifications)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Select);

                var result = await db.QueryAsync<TEntity>(selectQuery);

                if (paginationSpecifications.SearchValue != null)
                    result = result.AsQueryable().Where(paginationSpecifications.SearchValue);

                int totalCount = result.Count();
                result = result.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                var entities = result.ToList();
                return new PaginationList<TEntity>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
            }
        }

        public virtual async Task<List<TEntity>> GetAllAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Select);

                var result = await db.QueryAsync<TEntity>(selectQuery);

                if (repositorySpecifications != null)
                    if (repositorySpecifications.SearchValue != null)
                        result = result.AsQueryable().Where(repositorySpecifications.SearchValue);

                List<TEntity> entities = result.ToList();
                return entities;
            }
        }

        public virtual async Task<PaginationList<TEntity>> GetAllByNameAsync(string name, PaginationSpecifications<TEntity> paginationSpecifications)
        {
            using (IDbConnection db = _dbConnection)
            {
                string pattern = $"%{name}%";

                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Select);

                string searchQuery = GetSearchLikeExpression(NamesOfSearch, pattern);

                if (selectQuery.Contains("WHERE", StringComparison.OrdinalIgnoreCase))
                    selectQuery += $"{selectQuery} \nAnd\n ({searchQuery})";
                else
                    selectQuery += $"{selectQuery} \nWHERE\n ({searchQuery})";

                var result = await db.QueryAsync<TEntity>(selectQuery);

                result = result.AsQueryable();

                int totalCount = result.Count();
                result = result.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                var entities = result.ToList();
                return new PaginationList<TEntity>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
            }
        }

        public virtual async Task<List<IdAndName>> GetAsSelectedItemsAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery<TEntity>(BaseQueryBuilder.QueryType.Select);

                var result = await db.QueryAsync<TEntity>(selectQuery);

                if (repositorySpecifications != null)
                    if (repositorySpecifications.SearchValue != null)
                        result = result.AsQueryable().Where(repositorySpecifications.SearchValue);

                List<IdAndName> entities = result.Select(x => new IdAndName() { Id = x.Id, Name = NameSelector.Compile()(x) }).OrderBy(x => x.Name).ToList();
                return entities;
            }
        }

        public virtual async Task<int> GetMaxIdAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            using (IDbConnection db = _dbConnection)
            {
                string selectQuery = BaseQueryBuilder.BuildQuery(BaseQueryBuilder.QueryType.MaxId, null, repositorySpecifications?.SelectValue);

                var result = await db.QuerySingleOrDefaultAsync<int?>(selectQuery) ?? 0;
                return result;
            }
        }

        public virtual async Task<PaginationList<TEntityView>> GetAllViewAsync(PaginationSpecifications<TEntityView> paginationSpecifications)
        {
            using (IDbConnection db = _dbConnection)
            {
                if (typeof(TEntityView) == typeof(TEntity))
                {
                    var paginationSpecificationsValue = paginationSpecifications as PaginationSpecifications<TEntity>;

                    if (paginationSpecificationsValue == null)
                        throw new NotImplementedException($"The type of {typeof(TEntityView)} not equals {typeof(TEntity)}");

                    var result = await GetAllPaginationListAsync(paginationSpecificationsValue);

                    List<TEntityView> entities = result.Items.Cast<TEntityView>().ToList();
                    return new PaginationList<TEntityView>(entities, result.TotalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(ViewQuery))
                    {
                        string selectQuery = ViewQuery;

                        if (paginationSpecifications.SearchValue != null)
                        {
                            string fullTableName = $"[{DataConnection.DBName}].[dbo].[{typeof(TEntity).GetTableNameValue()}]";
                            if (!selectQuery.Contains("WHERE"))
                                selectQuery += "\nWHERE \n";
                            else
                                selectQuery += "\nAND \n";
                            selectQuery += BaseQueryBuilder.GetWhereClause(paginationSpecifications.SearchValue, fullTableName);
                        }

                        var globalQuery = GetGlobalFilter<TEntity>();
                        if (globalQuery != null)
                        {
                            string fullTableName = $"[{DataConnection.DBName}].[dbo].[{typeof(TEntity).GetTableNameValue()}]";
                            if (!selectQuery.Contains("WHERE"))
                                selectQuery += "\nWHERE \n";
                            else
                                selectQuery += "\nAND \n";
                            selectQuery += BaseQueryBuilder.GetWhereClause(globalQuery, fullTableName);
                        }

                        var result = await db.QueryAsync<TEntityView>(selectQuery);

                        if (paginationSpecifications.SearchValue != null)
                            result = result.AsQueryable().Where(paginationSpecifications.SearchValue);

                        if (paginationSpecifications.OrderByType == OrderByType.Descending)
                            result = result.AsQueryable().OrderByDescending(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));
                        else
                            result = result.AsQueryable().OrderBy(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));

                        int totalCount = result.Count();
                        result = result.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                        var entities = result.ToList();
                        return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                    }
                    else
                    {
                        throw new NotImplementedException($"Need to implement the {nameof(ViewQuery)} to load the view list");
                    }
                }
            }
        }

        public virtual async Task<PaginationList<TEntityView>> GetAllViewByNameAsync(string name, PaginationSpecifications<TEntityView> paginationSpecifications)
        {
            using (IDbConnection db = _dbConnection)
            {
                if (typeof(TEntityView) == typeof(TEntity))
                {
                    var paginationSpecificationsValue = paginationSpecifications as PaginationSpecifications<TEntity>;

                    if (paginationSpecificationsValue == null)
                        throw new NotImplementedException($"The type of {typeof(TEntityView)} not equals {typeof(TEntity)}");

                    var result = await GetAllByNameAsync(name, paginationSpecificationsValue);

                    List<TEntityView> entities = result.Items.Cast<TEntityView>().ToList();
                    return new PaginationList<TEntityView>(entities, result.TotalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(ViewQuery))
                    {
                        string selectQuery = ViewQuery;

                        if (paginationSpecifications.SearchValue != null)
                        {
                            string fullTableName = $"[{DataConnection.DBName}].[dbo].[{typeof(TEntity).GetTableNameValue()}]";
                            if (!selectQuery.Contains("WHERE"))
                                selectQuery += "\nWHERE \n";
                            else
                                selectQuery += "\nAND \n";
                            selectQuery += BaseQueryBuilder.GetWhereClause(paginationSpecifications.SearchValue, fullTableName);
                        }

                        var globalQuery = GetGlobalFilter<TEntity>();
                        if (globalQuery != null)
                        {
                            string fullTableName = $"[{DataConnection.DBName}].[dbo].[{typeof(TEntity).GetTableNameValue()}]";
                            if (!selectQuery.Contains("WHERE"))
                                selectQuery += "\nWHERE \n";
                            else
                                selectQuery += "\nAND \n";
                            selectQuery += BaseQueryBuilder.GetWhereClause(globalQuery, fullTableName);
                        }

                        string pattern = $"%{name}%";

                        string searchQuery = GetSearchLikeExpression(NamesOfSearch, pattern);

                        if (selectQuery.Contains("WHERE", StringComparison.OrdinalIgnoreCase))
                            selectQuery += $"{selectQuery} \nAnd\n ({searchQuery})";
                        else
                            selectQuery += $"{selectQuery} \nWHERE\n ({searchQuery})";

                        var result = await db.QueryAsync<TEntityView>(selectQuery);

                        if (paginationSpecifications.SearchValue != null)
                            result = result.AsQueryable().Where(paginationSpecifications.SearchValue);

                        result = result.AsQueryable().OrderBy(OrderByColumn ?? (x => x.Id));

                        int totalCount = result.Count();
                        result = result.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                        var entities = result.ToList();
                        return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                    }
                    else
                    {
                        throw new NotImplementedException($"Need to implement the {nameof(ViewQuery)} to load the view list");
                    }
                }
            }
        }
        #endregion

        #region Sync
        public TEntity? Add(TEntity? entity) => Task.Run(async () => await AddAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Update(TEntity? entity) => Task.Run(async () => await UpdateAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Remove(TEntity? entity) => Task.Run(async () => await RemoveAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Get(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public bool IsExist(Ulid id) => Task.Run(async () => await IsExistAsync(id)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllPaginationList(PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllPaginationListAsync(paginationSpecifications)).GetAwaiter().GetResult();

        public List<TEntity> GetAll(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAllAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllByName(string name, PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllByNameAsync(name, paginationSpecifications)).GetAwaiter().GetResult();

        public List<IdAndName> GetAsSelectedItems(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsSelectedItemsAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public int GetMaxId(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetMaxIdAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntityView> GetAllView(PaginationSpecifications<TEntityView> paginationSpecifications) => Task.Run(async () => await GetAllViewAsync(paginationSpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntityView> GetAllViewByName(string name, PaginationSpecifications<TEntityView> paginationSpecifications) => Task.Run(async () => await GetAllViewByNameAsync(name, paginationSpecifications)).GetAwaiter().GetResult();
        #endregion

        protected Expression<Func<TSource, bool>>? GetGlobalFilter<TSource>() => Utilities.GetGlobalFilter<TSource>(BaseQueryBuilder.GlobalFilters);
        protected virtual Expression<Func<TEntity, string>>[] NamesOfSearch { get; }
        protected virtual Expression<Func<TEntityView, string>>[] NamesOfSearchView { get; }
        protected virtual Expression<Func<TEntity, string>> NameSelector { get; }
        protected virtual string ViewQuery => string.Empty;
        protected virtual Expression<Func<TEntityView, object>> OrderByColumn { get; }

        private string GetSearchLikeExpression(Expression<Func<TEntity, string>>[] NamesOfSearch, string pattern)
        {
            List<string> conditions = new List<string>();

            foreach (var nameExpression in NamesOfSearch)
            {
                // Extract the property name from the expression
                string propertyName = ((MemberExpression)nameExpression.Body).Member.Name;

                // Create the SQL condition string for the LIKE clause
                string condition = $"{propertyName} LIKE N'{pattern}'";
                conditions.Add(condition);
            }

            // Combine all conditions with 'OR'
            string whereClause = string.Join(" OR ", conditions);

            return whereClause;
        }

        private string GetSearchLikeExpression(Expression<Func<TEntityView, string>>[] NamesOfSearch, string pattern)
        {
            List<string> conditions = new List<string>();

            foreach (var nameExpression in NamesOfSearch)
            {
                // Extract the property name from the expression
                string propertyName = ((MemberExpression)nameExpression.Body).Member.Name;

                // Create the SQL condition string for the LIKE clause
                string condition = $"{propertyName} LIKE N'{pattern}'";
                conditions.Add(condition);
            }

            // Combine all conditions with 'OR'
            string whereClause = string.Join(" OR ", conditions);

            return whereClause;
        }
    }
}
