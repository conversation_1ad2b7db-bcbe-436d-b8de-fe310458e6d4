﻿namespace GMCadiomCore.Models.ResultPattern
{
    /// <summary>
    /// Extension methods for Task<ServiceResult<T>> objects
    /// </summary>
    public static class TaskServiceResultExtensions
    {
        /// <summary>
        /// Extension method to get the data from a Task<ServiceResult<T>>
        /// </summary>
        /// <typeparam name="T">The type of data in the result</typeparam>
        /// <param name="task">The task to get data from</param>
        /// <returns>A task that returns the data from the result</returns>
        public static async Task<T?> GetDataAsync<T>(this Task<ServiceResult<T>> task)
        {
            var result = await task;
            return result.Data;
        }

        /// <summary>
        /// Extension method to get the data from a Task<ServiceResult<T>> and throw an exception if the result is not successful
        /// </summary>
        /// <typeparam name="T">The type of data in the result</typeparam>
        /// <param name="task">The task to get data from</param>
        /// <returns>A task that returns the data from the result</returns>
        /// <exception cref="Exception">Thrown if the result is not successful</exception>
        public static async Task<T> GetDataOrThrowAsync<T>(this Task<ServiceResult<T>> task)
        {
            var result = await task;
            result.ThrowIfFailure();
            return result.Data;
        }

        /// <summary>
        /// Extension method to get the data from a Task<ServiceResult<T>> or return a default value if the result is not successful
        /// </summary>
        /// <typeparam name="T">The type of data in the result</typeparam>
        /// <param name="task">The task to get data from</param>
        /// <param name="defaultValue">The default value to return if the result is not successful</param>
        /// <returns>A task that returns the data from the result or the default value</returns>
        public static async Task<T?> GetDataOrDefaultAsync<T>(this Task<ServiceResult<T>> task, T? defaultValue = default)
        {
            var result = await task;
            return result.IsSuccess ? result.Data : defaultValue;
        }

        /// <summary>
        /// Extension method to get the data from a Task<ServiceResult<T>> and throw an exception if the data is null
        /// </summary>
        /// <typeparam name="T">The type of data in the result</typeparam>
        /// <param name="task">The task to get data from</param>
        /// <param name="errorMessage">The error message to include in the exception</param>
        /// <returns>A task that returns the data from the result</returns>
        /// <exception cref="Exception">Thrown if the data is null</exception>
        public static async Task<T> GetDataOrThrowIfNullAsync<T>(this Task<ServiceResult<T>> task, string errorMessage = "Data is null")
        {
            var result = await task;
            result.ThrowIfFailure();

            if (result.Data == null)
                throw new Exception(errorMessage);

            return result.Data;
        }
    }
}
