﻿namespace SimpleBooks.Services.API.BaseService
{
    public class SimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> : BaseService<TEntity, TEntityView>, ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate>
        where TEntity : class, IBaseIdentityModel
        where TEntityView : class, IBaseIdentityModel
        where TEntityCreate : BaseCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
    {
        public SimpleBooksBaseService(IHttpClientFactory httpClientFactory) : base(httpClientFactory.CreateClient("SimpleBooks"))
        {
        }

        public override void EditModelBeforeSave(TEntity model)
        {
            base.EditModelBeforeSave(model);

            MakeSurePrimaryIdIsSet(model);

            Type entityType = model.GetType();
            var properties = entityType.GetProperties();
            foreach (var property in properties)
            {
                if (property.PropertyType.IsGenericType)
                {
                    var genericTypeDefinition = property.PropertyType.GetGenericTypeDefinition();

                    if (typeof(IEnumerable<>).IsAssignableFrom(genericTypeDefinition) ||
                        typeof(ICollection<>).IsAssignableFrom(genericTypeDefinition))
                    {
                        var collection = property.GetValue(model) as IEnumerable<object>;
                        if (collection != null)
                        {
                            foreach (var item in collection)
                            {
                                MakeSurePrimaryIdIsSet(item);
                            }
                        }
                    }
                }
            }
        }

        #region Async
        [HttpRequestMethod("AddAsync", HttpMethodEnum.POST, true)]
        public async Task<ServiceResult<TEntity?>> AddAsync(TEntityCreate model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<TEntity>>(this, model);
                if (result == null)
                    return ServiceResult<TEntity?>.Failure("Failed to add.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<TEntity?>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod("UpdateAsync", HttpMethodEnum.PUT, true)]
        public async Task<ServiceResult<TEntity?>> UpdateAsync(TEntityUpdate model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<TEntity>>(this, model);
                if (result == null)
                    return ServiceResult<TEntity?>.Failure("Failed to update.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<TEntity?>.Failure(ex.Message);
            }
        }
        #endregion

        #region Sync
        [HttpRequestMethod("Add", HttpMethodEnum.POST, true)]
        public ServiceResult<TEntity?> Add(TEntityCreate model) => Task.Run(async () => await AddAsync(model)).GetAwaiter().GetResult();

        [HttpRequestMethod("Update", HttpMethodEnum.PUT, true)]
        public ServiceResult<TEntity?> Update(TEntityUpdate model) => Task.Run(async () => await UpdateAsync(model)).GetAwaiter().GetResult();
        #endregion

        private void MakeSurePrimaryIdIsSet(object item)
        {
            var primaryIdProperty = item.GetType().GetProperty(nameof(BaseIdentityModel.Id));
            if (primaryIdProperty != null)
            {
                Ulid? currentPrimaryIdValue = primaryIdProperty.GetValue(item) as Ulid?;
                if (currentPrimaryIdValue != null)
                {
                    if (currentPrimaryIdValue == Ulid.Empty)
                        primaryIdProperty.SetValue(item, Ulid.NewUlid());
                }
            }
        }
    }
}
