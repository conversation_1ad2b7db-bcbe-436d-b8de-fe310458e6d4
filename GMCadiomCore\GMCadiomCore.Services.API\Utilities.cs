namespace GMCadiomCore.Services.API
{
    public static class Utilities
    {
        private class EndPointsMap
        {
            public EndPointsMap(string controllerName, string methodName, HttpMethodEnum httpMethod, bool useBody)
            {
                ControllerName = controllerName;
                MethodName = methodName;
                HttpMethod = httpMethod;
                UseBody = useBody;
            }

            public string ControllerName { get; }
            public string MethodName { get; }
            public HttpMethodEnum HttpMethod { get; }
            public bool UseBody { get; }

            public string EndPoint => $"{ControllerName}/{MethodName}";
        }

        private static readonly ConcurrentDictionary<string, EndPointsMap> _endpointsCache = new();

        public static void Initialize(Assembly assembly)
        {
            var services = assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<HttpRequestControllerAttribute>() != null)
                .ToList();

            foreach (var serviceType in services)
            {
                var controllerAttr = serviceType.GetCustomAttribute<HttpRequestControllerAttribute>();
                var genericType = serviceType.BaseType?.GetGenericArguments().FirstOrDefault();
                string controllerName = !string.IsNullOrEmpty(controllerAttr?.ControllerName)
                    ? controllerAttr.ControllerName
                    : ValidateValue.GetCleanName(genericType?.Name ?? serviceType.Name.Replace("Service", ""));

                if (string.IsNullOrEmpty(controllerName)) continue;

                foreach (var method in serviceType.GetMethods())
                {
                    var methodAttr = method.GetCustomAttribute<HttpRequestMethodAttribute>();
                    if (methodAttr == null) continue;

                    string key = $"{serviceType.FullName}.{method.Name}";
                    _endpointsCache[key] = new EndPointsMap(controllerName, methodAttr.MethodName, methodAttr.HttpMethod, methodAttr.UseBody);
                }
            }
        }

        public static async Task<string> SendRequest(this HttpClient httpClient, object caller, string? methodName, object? data = null)
        {
            string classType = caller.GetType().FullName;
            string key = $"{classType}.{methodName}";

            if (!_endpointsCache.TryGetValue(key, out var endpoint))
                throw new InvalidOperationException($"Endpoint for {key} not found in cache.");

            string url = endpoint.EndPoint;
            string fullUrl = endpoint.UseBody ? url : $"{url}{SerializeToQueryString(data)}";

            HttpResponseMessage response = endpoint.HttpMethod switch
            {
                HttpMethodEnum.GET => await httpClient.GetAsync(fullUrl),
                HttpMethodEnum.POST => await httpClient.PostAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.PUT => await httpClient.PutAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.PATCH => await httpClient.PatchAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.DELETE => await httpClient.DeleteAsync($"{url}/{SerializeToQueryString(data, true)}"),
                HttpMethodEnum.Options => await httpClient.SendAsync(
                    new HttpRequestMessage()
                    {
                        Method = HttpMethod.Options,
                        RequestUri = new Uri(fullUrl, UriKind.RelativeOrAbsolute),
                        Content = endpoint.UseBody ? CreateJsonContent(data) : null
                    }),
                _ => throw new NotSupportedException($"HTTP method {endpoint.HttpMethod} is not supported.")
            };

            // Check if response is successful
            if (!response.IsSuccessStatusCode)
            {
                string errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException($"HTTP {response.StatusCode}: {errorContent}");
            }

            return await response.Content.ReadAsStringAsync();
        }

        private static StringContent CreateJsonContent(object? data) =>
            new(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");

        private static string SerializeToQueryString(object? obj, bool isRoute = false)
        {
            if (obj == null) return string.Empty;

            var properties = obj.GetType().GetProperties();
            var queryString = string.Join("&", properties
                .Select(p =>
                {
                    var value = p.GetValue(obj);
                    if (value == null) return string.Empty;

                    return p.PropertyType == typeof(Ulid) || p.PropertyType == typeof(Ulid?)
                        ? (isRoute ? value.ToString() : $"{p.Name}={value}")
                        : $"{p.Name}={Uri.EscapeDataString(value.ToString() ?? string.Empty)}";
                })
                .Where(q => !string.IsNullOrEmpty(q)));

            return queryString.Length > 0 ? (isRoute ? queryString : "?" + queryString) : string.Empty;
        }

        private static bool IsServiceResultType(Type type)
        {
            if (type == typeof(ServiceResult))
                return true;

            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(ServiceResult<>))
                return true;

            return false;
        }

        private static TResult CreateFailureServiceResult<TResult>(string message, System.Net.HttpStatusCode statusCode)
        {
            var resultType = typeof(TResult);

            if (resultType == typeof(ServiceResult))
            {
                var result = ServiceResult.Failure(message, statusCode);
                return (TResult)(object)result;
            }

            if (resultType.IsGenericType && resultType.GetGenericTypeDefinition() == typeof(ServiceResult<>))
            {
                var dataType = resultType.GetGenericArguments()[0];
                var failureMethod = typeof(ServiceResult<>).MakeGenericType(dataType)
                    .GetMethod("Failure", new[] { typeof(string), typeof(System.Net.HttpStatusCode) });

                if (failureMethod != null)
                {
                    var result = failureMethod.Invoke(null, new object[] { message, statusCode });
                    return (TResult)result!;
                }
            }

            // Fallback - return default if not a ServiceResult type
            return default!;
        }

        public static async Task<TResult?> SendRequest<TResult>(this HttpClient httpClient, object caller, object? data = null, [CallerMemberName] string? methodName = null)
        {
            try
            {
                string? responseJson = await SendRequest(httpClient, caller, methodName, data);

                // Check if response is null or empty
                if (string.IsNullOrWhiteSpace(responseJson))
                {
                    // If TResult is ServiceResult or ServiceResult<T>, return failure result
                    if (IsServiceResultType(typeof(TResult)))
                    {
                        return CreateFailureServiceResult<TResult>("API returned null or empty response", System.Net.HttpStatusCode.NoContent);
                    }
                    return default;
                }

                // Define common serializer settings
                var serializerSettings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                };

                // Add Local DateTime converters for global API consistency
                serializerSettings.Converters.Add(new LocalDateTimeJsonConverter());
                serializerSettings.Converters.Add(new LocalNullableDateTimeJsonConverter());
                serializerSettings.Converters.Add(new LocalDateTimeOffsetJsonConverter());

                TResult? result = JsonConvert.DeserializeObject<TResult>(responseJson, serializerSettings);

                // If deserialization resulted in null and TResult is ServiceResult type
                if (result == null && IsServiceResultType(typeof(TResult)))
                {
                    return CreateFailureServiceResult<TResult>("Failed to deserialize API response", System.Net.HttpStatusCode.InternalServerError);
                }

                return result;
            }
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP-specific errors (including auth errors)
                if (IsServiceResultType(typeof(TResult)))
                {
                    var statusCode = System.Net.HttpStatusCode.InternalServerError;

                    // Check if it's an authentication error
                    if (httpEx.Message.Contains("401") || httpEx.Message.Contains("Unauthorized"))
                    {
                        statusCode = System.Net.HttpStatusCode.Unauthorized;
                    }
                    else if (httpEx.Message.Contains("403") || httpEx.Message.Contains("Forbidden"))
                    {
                        statusCode = System.Net.HttpStatusCode.Forbidden;
                    }
                    else if (httpEx.Message.Contains("404") || httpEx.Message.Contains("Not Found"))
                    {
                        statusCode = System.Net.HttpStatusCode.NotFound;
                    }

                    return CreateFailureServiceResult<TResult>($"API request failed: {httpEx.Message}", statusCode);
                }
                return default;
            }
            catch (JsonException jsonEx)
            {
                // Handle JSON deserialization errors
                if (IsServiceResultType(typeof(TResult)))
                {
                    return CreateFailureServiceResult<TResult>($"Failed to parse API response: {jsonEx.Message}", System.Net.HttpStatusCode.InternalServerError);
                }
                return default;
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                if (IsServiceResultType(typeof(TResult)))
                {
                    return CreateFailureServiceResult<TResult>($"Unexpected error occurred: {ex.Message}", System.Net.HttpStatusCode.InternalServerError);
                }
                return default;
            }
        }
    }
}
