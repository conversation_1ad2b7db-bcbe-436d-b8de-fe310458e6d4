﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckTreasuryVoucherService : SimpleBooksBaseService<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel, CreateCheckTreasuryVoucherViewModel, UpdateCheckTreasuryVoucherViewModel>, ICheckTreasuryVoucherService
    {
        private readonly IVendorService _vendorService;
        private readonly ICustomerService _customerService;
        private readonly IEmployeeService _employeeService;
        private readonly ICheckVaultService _checkVaultService;
        private readonly ICheckVaultLocationService _checkVaultLocationService;
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly IExpensesService _expensesService;
        private readonly IBillService _billService;
        private readonly IBillReturnService _billReturnService;
        private readonly IInvoiceService _invoiceService;
        private readonly IInvoiceReturnService _invoiceReturnService;

        public CheckTreasuryVoucherService(

            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            ICustomerService customerService,
            IEmployeeService employeeService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService,
            IBankService bankService,
            IBankAccountService bankAccountService,
            IExpensesService expensesService,
            IBillService billService,
            IBillReturnService billReturnService,
            IInvoiceService invoiceService,
            IInvoiceReturnService invoiceReturnService) : base(httpClientFactory)
        {
            _vendorService = vendorService;
            _customerService = customerService;
            _employeeService = employeeService;
            _checkVaultService = checkVaultService;
            _checkVaultLocationService = checkVaultLocationService;
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _expensesService = expensesService;
            _billService = billService;
            _billReturnService = billReturnService;
            _invoiceService = invoiceService;
            _invoiceReturnService = invoiceReturnService;
        }

        public async Task<ServiceResult<IEnumerable<BeneficiaryTypeEnumeration>>> SelectiveBeneficiaryTypeListAsync() => await Task.FromResult(BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorListAsync() => await _vendorService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerListAsync() => await _customerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveEmployeeListAsync() => await _employeeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TransactionTypeEnumeration>>> SelectiveTransactionTypeListAsync() => await Task.FromResult(TransactionTypeEnumeration.TreasuryCheckTransactionTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync() => await _checkVaultService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync() => await _checkVaultLocationService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<TreasuryLineTypeEnumeration>>> SelectiveTreasuryLineTypeListAsync() => await Task.FromResult(TreasuryLineTypeEnumeration.TreasuryLineTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveExpenseListAsync() => await _expensesService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillListAsync() => await _billService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillReturnListAsync() => await _billReturnService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceListAsync() => await _invoiceService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceReturnListAsync() => await _invoiceReturnService.GetSelectListAsync();

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecksPaginationList), HttpMethodEnum.GET)]
        public async Task<ServiceResult<PaginationList<CheckTreasuryVoucherModel>>> GetAllTreasuryVoucherChecksPaginationList(int pageNumber, int pageSize = 25)
        {
            var result = await _httpClient.SendRequest<ServiceResult<PaginationList<CheckTreasuryVoucherModel>>>(this, new { pageNumber, pageSize });
            if (result == null || result.Data == null)
                return ServiceResult<PaginationList<CheckTreasuryVoucherModel>>.Failure("No treasury vouchers found.");
            return result;
        }

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> GetAllTreasuryVoucherChecks(Ulid? checkStatusId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>>(this, new { checkStatusId }) ?? new List<CheckTreasuryVoucherModel>();
            return result;
        }

        [HttpRequestMethod(nameof(GetTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<CheckTreasuryVoucherModel?>> GetTreasuryVoucherChecks(string checkId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<CheckTreasuryVoucherModel?>>(this, new { checkId });
            if (result == null || result.Data == null)
                return ServiceResult<CheckTreasuryVoucherModel?>.Failure(checkId + " not found.");
            return result;
        }

        [HttpRequestMethod(nameof(SelectiveCheckTreasuryVoucherDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveCheckTreasuryVoucherDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TreasuryVoucherDto>>>(this) ?? new List<TreasuryVoucherDto>();
            return result;
        }

        [HttpRequestMethod(nameof(SelectiveCheckTreasuryVouchersAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchersAsync(Ulid checkStatusId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>>(this, new { checkStatusId }) ?? new List<CheckTreasuryVoucherModel>();
            return result;
        }

        [HttpRequestMethod(nameof(SelectiveDepositCheckTreasuryVouchersAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveDepositCheckTreasuryVouchersAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>>(this) ?? new List<CheckTreasuryVoucherModel>();
            return result;
        }

        [HttpRequestMethod(nameof(GetByIdJsonAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            string result = await _httpClient.SendRequest<ServiceResult<string>>(this, new { id }) ?? string.Empty;
            return result;
        }
    }
}
