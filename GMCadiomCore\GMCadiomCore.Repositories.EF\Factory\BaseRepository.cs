﻿namespace GMCadiomCore.Repositories.EF.Factory
{
    public class BaseRepository<TEntity, TEntityView> : IBaseRepository<TEntity, TEntityView> where TEntity : class, IBaseIdentityModel where TEntityView : class, IBaseIdentityModel
    {
        protected readonly DbContext _context;
        public BaseRepository(DbContext context)
        {
            _context = context;
        }

        #region Async
        public virtual async Task<TEntity?> AddAsync(TEntity? entity)
        {
            if (entity != null)
            {
                await _context.Set<TEntity>().AddAsync(entity);
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public virtual async Task<TEntity?> UpdateAsync(TEntity? entity)
        {
            if (entity == null) return null;

            var keyProperties = GetPrimaryKeys(_context, entity);
            if (keyProperties == null) return null;

            // Try to find an already tracked entity with the same primary key
            var trackedEntity = _context.ChangeTracker.Entries<TEntity>()
                .FirstOrDefault(e => e.State != EntityState.Detached && KeysMatch(e.Entity, entity, keyProperties));

            if (trackedEntity != null)
            {
                // Copy values from incoming entity to the tracked one
                trackedEntity.CurrentValues.SetValues(entity);
                trackedEntity.State = EntityState.Modified;
            }
            else
            {
                // Ensure nothing is tracked with the same key
                var keyValues = keyProperties.Select(p => p.GetValue(entity)).ToArray();
                var existing = await _context.Set<TEntity>().FindAsync(keyValues);
                if (existing != null)
                {
                    // Copy values to the existing tracked entity
                    _context.Entry(existing).CurrentValues.SetValues(entity);
                    _context.Entry(existing).State = EntityState.Modified;
                }
                else
                {
                    // Not tracked and not in DB, just attach and mark as modified
                    _context.Attach(entity);
                    _context.Entry(entity).State = EntityState.Modified;
                }
            }

            await _context.SaveChangesAsync();
            return entity;
        }

        public virtual async Task<TEntity?> RemoveAsync(TEntity? entity)
        {
            if (entity != null)
            {
                //new DeleteValidation(UnitOfWork.GetInstance(), entity);

                _context.Set<TEntity>().Remove(entity);
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public virtual async Task<TEntity?> GetAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            IQueryable<TEntity> query = GetQueryable(repositorySpecifications);
            TEntity? entity = await query.FirstOrDefaultAsync();
            return entity;
        }

        public virtual async Task<bool> IsExistAsync(Ulid id)
        {
            return await _context.Set<TEntity>().AnyAsync(x => x.Id == id);
        }

        public virtual async Task<PaginationList<TEntity>> GetAllPaginationListAsync(PaginationSpecifications<TEntity> paginationSpecifications)
        {
            IQueryable<TEntity> query = GetQueryable(paginationSpecifications);
            int totalCount = await query.CountAsync();
            query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
            var entities = await query.ToListAsync();
            return new PaginationList<TEntity>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
        }

        public virtual async Task<List<TEntity>> GetAllAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            IQueryable<TEntity> query = GetQueryable(repositorySpecifications);
            List<TEntity> entities = await query.ToListAsync();
            return entities;
        }

        public virtual async Task<PaginationList<TEntity>> GetAllByNameAsync(string name, PaginationSpecifications<TEntity> paginationSpecifications)
        {
            string pattern = $"%{name}%";
            IQueryable<TEntity> query = GetQueryable(paginationSpecifications).AsNoTracking();
            query = query.Where(GetSearchLikeExpression(NamesOfSearch, pattern));
            int totalCount = await query.CountAsync();
            query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
            var entities = await query.ToListAsync();
            return new PaginationList<TEntity>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
        }

        public virtual async Task<List<IdAndName>> GetAsSelectedItemsAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            IQueryable<TEntity> query = GetQueryable(repositorySpecifications).AsNoTracking();

            List<IdAndName> entities = new List<IdAndName>();

            var name = GetIdAndNameExpression(NameSelector);
            if (name != null)
            {
                entities = await query.Select(name).OrderBy(x => x.Name).ToListAsync();
            }
            return entities;
        }

        public virtual async Task<int> GetMaxIdAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            if (repositorySpecifications != null && repositorySpecifications.SelectValue != null)
            {
                int? result = await _context.Set<TEntity>().Select(repositorySpecifications.SelectValue).MaxAsync() as int?;

                return await Task.FromResult(ValidateValue.ValidateInt(result));
            }
            else
            {
                var entityType = _context.Model.FindEntityType(typeof(TEntity));
                if (entityType != null)
                {
                    var key = entityType.FindPrimaryKey();
                    if (key != null)
                    {
                        var keyProperty = key.Properties.FirstOrDefault();
                        if (keyProperty != null)
                        {
                            PropertyInfo? propertyInfo = keyProperty.PropertyInfo;
                            if (propertyInfo != null)
                            {
                                var parameter = Expression.Parameter(typeof(TEntity), "x");
                                var property = Expression.Property(parameter, propertyInfo);
                                var selector = Expression.Lambda<Func<TEntity, int>>(property, parameter);

                                var maxId = await _context.Set<TEntity>()
                                                    .Select(selector)
                                                    .DefaultIfEmpty()
                                                    .MaxAsync();
                                return maxId;
                            }
                        }
                    }
                }
            }
            return 0;
        }

        public virtual async Task<PaginationList<TEntityView>> GetAllViewAsync(PaginationSpecifications<TEntityView> paginationSpecifications)
        {
            if (typeof(TEntityView) == typeof(TEntity))
            {
                var repositorySpecificationsValue = paginationSpecifications as PaginationSpecifications<TEntity>;

                if (repositorySpecificationsValue == null)
                    throw new NotImplementedException($"The type of {typeof(TEntityView)} not equals {typeof(TEntity)}");

                var result = await GetAllPaginationListAsync(repositorySpecificationsValue);

                List<TEntityView> entities = result.Items.Cast<TEntityView>().ToList();
                return new PaginationList<TEntityView>(entities, result.TotalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
            }
            else
            {
                if (ViewQueryable != null)
                {
                    var query = ViewQueryable;

                    if (paginationSpecifications.SearchValue != null)
                        query = query.Where(paginationSpecifications.SearchValue);
                    var globalQuery = GetGlobalFilter<TEntityView>();
                    if (globalQuery != null)
                        query = query.Where(globalQuery);

                    if (paginationSpecifications.OrderByType == OrderByType.Descending)
                        query = query.OrderByDescending(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));
                    else
                        query = query.OrderBy(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));

                    int totalCount = await query.CountAsync();
                    query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                    var entities = await query.ToListAsync();
                    return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else if (!string.IsNullOrWhiteSpace(ViewQuery))
                {
                    var query = _context.Database.SqlQuery<TEntityView>(ToFormattableString(ViewQuery));

                    if (paginationSpecifications.SearchValue != null)
                        query = query.Where(paginationSpecifications.SearchValue);
                    var globalQuery = GetGlobalFilter<TEntityView>();
                    if (globalQuery != null)
                        query = query.Where(globalQuery);

                    if (paginationSpecifications.OrderByType == OrderByType.Descending)
                        query = query.OrderByDescending(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));
                    else
                        query = query.OrderBy(paginationSpecifications.OrderBy ?? OrderByColumn ?? (x => x.Id));

                    int totalCount = await query.CountAsync();
                    query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                    var entities = await query.ToListAsync();
                    return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else
                {
                    throw new NotImplementedException($"Need to implement the {nameof(ViewQuery)} to load the view list");
                }
            }
        }

        public virtual async Task<PaginationList<TEntityView>> GetAllViewByNameAsync(string name, PaginationSpecifications<TEntityView> paginationSpecifications)
        {
            if (typeof(TEntityView) == typeof(TEntity))
            {
                var repositorySpecificationsValue = paginationSpecifications as PaginationSpecifications<TEntity>;

                if (repositorySpecificationsValue == null)
                    throw new NotImplementedException($"The type of {typeof(TEntityView)} not equals {typeof(TEntity)}");

                var result = await GetAllByNameAsync(name, repositorySpecificationsValue);

                List<TEntityView> entities = result.Items.Cast<TEntityView>().ToList();
                return new PaginationList<TEntityView>(entities, result.TotalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
            }
            else
            {
                if (ViewQueryable != null)
                {
                    var query = ViewQueryable;

                    if (paginationSpecifications.SearchValue != null)
                        query = query.Where(paginationSpecifications.SearchValue);
                    var globalQuery = GetGlobalFilter<TEntityView>();
                    if (globalQuery != null)
                        query = query.Where(globalQuery);

                    string pattern = $"%{name}%";
                    query = query.Where(GetSearchLikeExpression<TEntityView>(NamesOfSearchView, pattern));

                    query = query.OrderBy(OrderByColumn ?? (x => x.Id));

                    int totalCount = await query.CountAsync();
                    query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                    var entities = await query.ToListAsync();
                    return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else if (!string.IsNullOrWhiteSpace(ViewQuery))
                {
                    var query = _context.Database.SqlQuery<TEntityView>(ToFormattableString(ViewQuery));

                    if (paginationSpecifications.SearchValue != null)
                        query = query.Where(paginationSpecifications.SearchValue);
                    var globalQuery = GetGlobalFilter<TEntityView>();
                    if (globalQuery != null)
                        query = query.Where(globalQuery);

                    string pattern = $"%{name}%";
                    query = query.Where(GetSearchLikeExpression<TEntityView>(NamesOfSearchView, pattern));

                    query = query.OrderBy(OrderByColumn ?? (x => x.Id));

                    int totalCount = await query.CountAsync();
                    query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                    var entities = await query.ToListAsync();
                    return new PaginationList<TEntityView>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                }
                else
                {
                    throw new NotImplementedException($"Need to implement the {nameof(ViewQuery)} to load the view list");
                }
            }
        }
        #endregion

        #region Sync
        public TEntity? Add(TEntity? entity) => Task.Run(async () => await AddAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Update(TEntity? entity) => Task.Run(async () => await UpdateAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Remove(TEntity? entity) => Task.Run(async () => await RemoveAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Get(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public bool IsExist(Ulid id) => Task.Run(async () => await IsExistAsync(id)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllPaginationList(PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllPaginationListAsync(paginationSpecifications)).GetAwaiter().GetResult();

        public List<TEntity> GetAll(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAllAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllByName(string name, PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllByNameAsync(name, paginationSpecifications)).GetAwaiter().GetResult();

        public List<IdAndName> GetAsSelectedItems(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsSelectedItemsAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public int GetMaxId(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetMaxIdAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntityView> GetAllView(PaginationSpecifications<TEntityView> paginationSpecifications) => Task.Run(async () => await GetAllViewAsync(paginationSpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntityView> GetAllViewByName(string name, PaginationSpecifications<TEntityView> paginationSpecifications) => Task.Run(async () => await GetAllViewByNameAsync(name, paginationSpecifications)).GetAwaiter().GetResult();
        #endregion

        protected Expression<Func<TSource, bool>>? GetGlobalFilter<TSource>() => Utilities.GetGlobalFilter<TSource>(BaseQueryBuilder.GlobalFilters);
        protected virtual Expression<Func<TEntity, string>>[] NamesOfSearch { get; }
        protected virtual Expression<Func<TEntityView, string>>[] NamesOfSearchView { get; }
        protected virtual Expression<Func<TEntity, string>> NameSelector { get; }
        protected virtual IQueryable<TEntityView> ViewQueryable { get; }
        protected virtual string ViewQuery => string.Empty;
        protected virtual Expression<Func<TEntityView, object>> OrderByColumn { get; }

        protected IQueryable<T> GetQueryable<T>(RepositorySpecifications<T>? repositorySpecifications) where T : class, IBaseIdentityModel
        {
            if (repositorySpecifications == null)
                return _context.Set<T>();

            IQueryable<T> query = _context.Set<T>();

            if (repositorySpecifications.IsTackable == false)
                query = query.AsNoTracking();

            if (repositorySpecifications.Includes != null)
                query = repositorySpecifications.Includes(query);

            if (repositorySpecifications.SearchValue != null)
                query = query.Where(repositorySpecifications.SearchValue);

            if (repositorySpecifications.OrderByType == OrderByType.Descending)
                query = query.OrderByDescending(repositorySpecifications.OrderBy ?? (x => x.Id));
            else
                query = query.OrderBy(repositorySpecifications.OrderBy ?? (x => x.Id));

            var globalQuery = GetGlobalFilter<T>();
            if (globalQuery != null)
                query = query.Where(globalQuery);

            return query;
        }

        private Expression<Func<TEntity, IdAndName>>? GetIdAndNameExpression(Expression<Func<TEntity, string>> nameSelector)
        {
            // Define the parameter for the expression (e.g., x => ...)
            var parameter = Expression.Parameter(typeof(TEntity), "x");

            // Access the Id property directly (e.g., x.Id)
            var idProperty = Expression.Property(parameter, "Id");

            // Compile the nameSelector to access the Name property (e.g., NameSelector.Compile()(x))
            var compiledNameSelector = Expression.Invoke(nameSelector, parameter);

            // Create MemberBindings for the Id and Name properties
            PropertyInfo? idPropertyInfo = typeof(IdAndName).GetProperty("Id");
            PropertyInfo? namePropertyInfo = typeof(IdAndName).GetProperty("Name");
            if (idPropertyInfo != null && namePropertyInfo != null)
            {
                var idBinding = Expression.Bind(idPropertyInfo, idProperty);
                var nameBinding = Expression.Bind(namePropertyInfo, compiledNameSelector);

                // Create a new IdAndName object and set its properties using the bindings
                var newIdAndName = Expression.MemberInit(
                    Expression.New(typeof(IdAndName)),
                    idBinding,
                    nameBinding
                );

                // Compile the final expression into a lambda (e.g., x => new IdAndName { Id = x.Id, Name = NameSelector.Compile()(x) })
                var lambda = Expression.Lambda<Func<TEntity, IdAndName>>(newIdAndName, parameter);

                return lambda;
            }
            return null;
        }

        private Expression<Func<T, bool>> GetSearchLikeExpression<T>(Expression<Func<T, string>>[] NamesOfSearch, string pattern)
        {
            // Define the parameter for the expression (e.g., x => ...)
            var parameter = Expression.Parameter(typeof(T), "x");
            Expression combinedExpression = Expression.Constant(false);

            foreach (var nameExpression in NamesOfSearch)
            {
                // Access the property directly without Invoke
                var propertyAccess = Expression.Property(parameter, ((MemberExpression)nameExpression.Body).Member.Name);

                // Create the Like expression using EF.Functions.Like
                var likeExpression = Expression.Call(
                    typeof(DbFunctionsExtensions),
                    nameof(DbFunctionsExtensions.Like),
                    null,
                    Expression.Constant(Microsoft.EntityFrameworkCore.EF.Functions),
                    propertyAccess,
                    Expression.Constant(pattern)
                );

                // Combine the expressions with OR
                combinedExpression = Expression.OrElse(combinedExpression, likeExpression);
            }

            // Compile the final expression into a lambda
            var lambda = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);

            return lambda;
        }

        private static IReadOnlyList<PropertyInfo> GetPrimaryKeys(DbContext context, TEntity entity)
        {
            var entityType = context.Model.FindEntityType(typeof(TEntity));
            return entityType?.FindPrimaryKey()?.Properties
                .Select(p => typeof(TEntity).GetProperty(p.Name))
                .Where(p => p != null)
                .ToList()
                ?? new List<PropertyInfo>();
        }

        private bool KeysMatch(TEntity a, TEntity b, IReadOnlyList<PropertyInfo> keys)
        {
            foreach (var key in keys)
            {
                var aValue = key.GetValue(a);
                var bValue = key.GetValue(b);
                if (!Equals(aValue, bValue))
                    return false;
            }
            return true;
        }

        private FormattableString ToFormattableString(string value) => FormattableStringFactory.Create(value);

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
