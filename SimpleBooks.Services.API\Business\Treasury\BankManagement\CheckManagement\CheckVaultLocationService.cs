﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultLocationService : SimpleBooksBaseService<CheckVaultLocationModel, CheckVaultLocationModel, CreateCheckVaultLocationViewModel, UpdateCheckVaultLocationViewModel>, ICheckVaultLocationService
    {
        public CheckVaultLocationService(IHttpClientFactory httpClientFactory) : base(httpClientFactory)
        {
        }
    }
}
