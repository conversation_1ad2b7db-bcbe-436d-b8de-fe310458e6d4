namespace GMCadiomCore.Models.Abstractions
{
    public interface IBaseScreensAccessProfileDetailsModel : IBaseIdentityModel
    {
        Ulid ScreensAccessProfileId { get; set; }
        Ulid ScreenId { get; set; }
        bool CanShow { get; set; }
        bool CanOpen { get; set; }
        bool CanAdd { get; set; }
        bool CanEdit { get; set; }
        bool CanDelete { get; set; }
        bool CanPrint { get; set; }
    }
}