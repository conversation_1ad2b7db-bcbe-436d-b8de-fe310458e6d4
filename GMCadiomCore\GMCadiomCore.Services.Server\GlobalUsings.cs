﻿global using GMCadiomCore.Models.Abstractions;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ModelValidation.Base;
global using GMCadiomCore.Models.ModelValidation.CustomAttribute;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Repositories.Factory;
global using GMCadiomCore.Repositories.IRepository;
global using GMCadiomCore.Services.Core;
global using GMCadiomCore.Services.Server.Helper;
global using GMCadiomCore.Shared.Helper;
global using Microsoft.EntityFrameworkCore.Query;
global using System.Collections;
global using System.ComponentModel;
global using System.Linq.Expressions;
global using System.Reflection;
