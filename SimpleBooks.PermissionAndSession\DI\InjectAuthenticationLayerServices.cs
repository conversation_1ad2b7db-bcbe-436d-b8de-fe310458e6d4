﻿namespace SimpleBooks.PermissionAndSession.DI
{
    public static class InjectAuthenticationLayerServices
    {
        public static IServiceCollection InjectApiAuthenticationServices(this IServiceCollection services)
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("JWTAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("SessionAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("DataBaseAppSettings.json", optional: false, reloadOnChange: true)
                .Build();

            JWTAppSettings jwtAppSettings = configuration.GetSection("JWT").Get<JWTAppSettings>()
                ?? throw new Exception("Need To Implement JWTAppSettings");
            services.AddSingleton(jwtAppSettings);

            DataBaseAppSettings dataBaseAppSettings = configuration.GetSection("ConnectionStrings").Get<DataBaseAppSettings>()
                ?? throw new Exception("Need To Implement DataBaseAppSettings");
            services.AddSingleton(dataBaseAppSettings);

            SessionAppSettings sessionAppSettings = configuration.GetSection("Session").Get<SessionAppSettings>()
                ?? throw new Exception("Need To Implement SessionAppSettings");
            services.AddSingleton(sessionAppSettings);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = AuthenticationDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = AuthenticationDefaults.AuthenticationScheme;
                options.DefaultForbidScheme = AuthenticationDefaults.AuthenticationScheme;
                options.DefaultScheme = AuthenticationDefaults.AuthenticationScheme;
                options.DefaultSignInScheme = AuthenticationDefaults.AuthenticationScheme;
                options.DefaultSignOutScheme = AuthenticationDefaults.AuthenticationScheme;
            }).AddJwtBearer(AuthenticationDefaults.AuthenticationScheme, options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                // Set up token mechanism
                options.TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateIssuer = true,
                    ValidIssuer = jwtAppSettings.Issuer,
                    ValidateAudience = true,
                    ValidAudience = jwtAppSettings.Audience,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtAppSettings.Key)),
                    ClockSkew = TimeSpan.Zero,
                };
            });

            services.AddAuthorization();

            return services;
        }

        public static IServiceCollection InjectWebAuthenticationServices(this IServiceCollection services)
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("JWTAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("SessionAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("DataBaseAppSettings.json", optional: false, reloadOnChange: true)
                .Build();

            JWTAppSettings jwtAppSettings = configuration.GetSection("JWT").Get<JWTAppSettings>()
                ?? throw new Exception("Need To Implement JWTAppSettings");
            services.AddSingleton(jwtAppSettings);

            DataBaseAppSettings dataBaseAppSettings = configuration.GetSection("ConnectionStrings").Get<DataBaseAppSettings>()
                ?? throw new Exception("Need To Implement DataBaseAppSettings");
            services.AddSingleton(dataBaseAppSettings);

            SessionAppSettings sessionAppSettings = configuration.GetSection("Session").Get<SessionAppSettings>()
                ?? throw new Exception("Need To Implement SessionAppSettings");
            services.AddSingleton(sessionAppSettings);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = AuthenticationDefaults.CookieAuthenticationHeader;
                options.DefaultChallengeScheme = AuthenticationDefaults.CookieAuthenticationHeader;
            }).AddCookie(AuthenticationDefaults.CookieAuthenticationHeader, options =>
            {
                options.LoginPath = "/Authentication/Login";
                options.AccessDeniedPath = "/Authentication/UnAuthorizedIndex";
                options.ExpireTimeSpan = TimeSpan.FromHours(1);
                options.SlidingExpiration = true;
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
                options.Cookie.SameSite = SameSiteMode.Lax;
                options.Cookie.Name = AuthenticationDefaults.CookieAuthenticationHeader;
            });

            services.AddAuthorization();

            return services;
        }
    }
}
