﻿namespace SimpleBooks.Services.API.Business.Purchases
{
    public class PurchaseOrderService : SimpleBooksBaseService<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>, IPurchaseOrderService
    {
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public PurchaseOrderService(

            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(httpClientFactory)
        {
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();


        [HttpRequestMethod(nameof(GetBilledQuantitiesAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<Dictionary<string, decimal>>> GetBilledQuantitiesAsync(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<Dictionary<string, decimal>>>(this, new { purchaseOrderId });
                if (result == null)
                    return ServiceResult<Dictionary<string, decimal>>.Failure("No data found or request failed.");
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpRequestMethod(nameof(GetBillDetailsByOrderAndProductAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<BillDetailsDto>>> GetBillDetailsByOrderAndProductAsync(Ulid purchaseOrderId, Ulid productId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<BillDetailsDto>>>(this, new { purchaseOrderId, productId });
                if (result == null)
                    return ServiceResult<IEnumerable<BillDetailsDto>>.Failure("No data found or request failed.");
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
