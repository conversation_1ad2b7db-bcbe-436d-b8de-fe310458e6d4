﻿namespace GMCadiomCore.Models.ResultPattern
{
    public class ServiceResult<T> : ServiceResult
    {
        public T? Data { get; set; }

        public static ServiceResult<T> Success(T data, string message = "Operation completed successfully")
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                StatusCode = HttpStatusCode.OK,
                Message = message,
                Data = data
            };
        }

        public static new ServiceResult<T> Failure(string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                StatusCode = statusCode,
                Message = message
            };
        }

        public static ServiceResult<T> Failure()
        {
            return new ServiceResult<T>
            {
                Data = default,
                IsSuccess = false,
                StatusCode = HttpStatusCode.OK,
                Message = "Fail To Call API So Return Default Value"
            };
        }

        public static new ServiceResult<T> ValidationFailure(Dictionary<string, string[]> validationErrors, string message = "Validation failed")
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                StatusCode = HttpStatusCode.BadRequest,
                Message = message,
                Errors = validationErrors
            };
        }

        public static implicit operator T(ServiceResult<T> result)
        {
            return (result != null && result.Data != null) ? result.Data : default;
        }

        public static implicit operator ServiceResult<T>(T data)
        {
            return new ServiceResult<T> { Data = data, IsSuccess = true };
        }
    }

    public class ServiceResult
    {
        public bool IsSuccess { get; set; }

        public HttpStatusCode StatusCode { get; set; }

        public string Message { get; set; } = string.Empty;

        public Dictionary<string, string[]>? Errors { get; set; }

        public string GetErrorMessage()
        {
            if (Errors == null || Errors.Count == 0)
                return Message;
            var errorMessages = new List<string>();
            foreach (var error in Errors)
            {
                errorMessages.Add($"{error.Key}: {string.Join(", ", error.Value)}");
            }
            return string.Join(Environment.NewLine, errorMessages);
        }

        public void ThrowIfFailure()
        {
            if (!IsSuccess)
            {
                throw new Exception(GetErrorMessage());
            }
        }

        public static ServiceResult Success(string message = "Operation completed successfully")
        {
            return new ServiceResult
            {
                IsSuccess = true,
                StatusCode = HttpStatusCode.OK,
                Message = message
            };
        }

        public static ServiceResult Failure(string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
        {
            return new ServiceResult
            {
                IsSuccess = false,
                StatusCode = statusCode,
                Message = message
            };
        }

        public static ServiceResult ValidationFailure(Dictionary<string, string[]> validationErrors, string message = "Validation failed")
        {
            return new ServiceResult
            {
                IsSuccess = false,
                StatusCode = HttpStatusCode.BadRequest,
                Message = message,
                Errors = validationErrors
            };
        }
    }
}
