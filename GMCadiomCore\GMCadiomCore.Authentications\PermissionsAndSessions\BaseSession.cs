﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public class BaseSession
    {
        public static bool OpenFormAuthorization(string OpenFormName)
        {
            var screen = ScreensAccesses.SingleOrDefault(x => x.ScreenName == OpenFormName);

            if (screen != null)
            {
                return true;
            }
            else
            {
                throw new Exception("غير مصرح لك");
            }
        }

        public static bool CheckActionAuthorization(string formName, Actions actions, IBaseUserModel? user = null)
        {
            if (user == null)
                user = User;

            if (user.UserTypeId == UserTypeEnumeration.Admin.Value)
                return true;
            else
            {
                var screen = ScreensAccesses.SingleOrDefault(x => x.ScreenName == formName);
                bool flag = true;
                if (screen != null)
                {
                    switch (actions)
                    {
                        case Actions.Add:
                            flag = screen.CanAdd;
                            break;
                        case Actions.Edit:
                            flag = screen.CanEdit;
                            break;
                        case Actions.Delete:
                            flag = screen.CanDelete;
                            break;
                        case Actions.Print:
                            flag = screen.CanPrint;
                            break;
                        default:
                            break;
                    }
                }
                if (flag == false)
                {
                    throw new Exception("غير مصرح لك");
                }
                return flag;
            }
        }

        public static void SetUser(IBaseUserModel user, List<BaseScreensAccessTemplate> getScreens, IBaseScreensAccessProfileModel getScreensProfile)
        {
            _user = user;

            BaseScreens.AddScreen(getScreens);

            if (user.UserTypeId == UserTypeEnumeration.Admin.Value)
                _screensAccesses = BaseScreens.GetScreens;
            else
                _screensAccesses = (from gs in BaseScreens.GetScreens
                                    from db in getScreensProfile.ScreensAccessProfileDetails
                                    .Where(x => x.ScreenId == gs.ScreenId).DefaultIfEmpty()
                                    select new BaseScreensAccessTemplate(gs.ScreenId, null, gs.ScreenName)
                                    {
                                        ScreenId = gs.ScreenId,
                                        HasChild = (db == null) ? false : gs.HasChild,
                                        ParentScreenId = gs.ParentScreenId,
                                        ScreenName = gs.ScreenName,
                                        ScreenText = gs.ScreenText,
                                        CanAdd = (db == null) ? true : db.CanAdd,
                                        CanDelete = (db == null) ? true : db.CanDelete,
                                        CanEdit = (db == null) ? true : db.CanEdit,
                                        CanOpen = (db == null) ? true : db.CanOpen,
                                        CanPrint = (db == null) ? true : db.CanPrint,
                                        CanShow = (db == null) ? true : db.CanShow,
                                        Actions = gs.Actions,
                                    }).ToList();
        }

        private static IBaseUserModel _user;
        public static IBaseUserModel User { get { return _user; } }

        private static List<BaseScreensAccessTemplate> _screensAccesses;
        public static List<BaseScreensAccessTemplate> ScreensAccesses { get { return _screensAccesses; } }
    }
}
