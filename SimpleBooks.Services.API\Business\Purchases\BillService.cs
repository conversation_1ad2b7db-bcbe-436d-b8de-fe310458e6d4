﻿namespace SimpleBooks.Services.API.Business.Purchases
{
    public class BillService : SimpleBooksBaseService<BillModel, IndexBillViewModel, CreateBillViewModel, UpdateBillViewModel>, IBillService
    {
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public BillService(

            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(httpClientFactory)
        {
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();

        [HttpRequestMethod(nameof(GetOpenPurchaseOrdersByVendorAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderDto>>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<OpenPurchaseOrderDto>>>(this, new { vendorId }) ?? new List<OpenPurchaseOrderDto>();
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod(nameof(GetOpenPurchaseOrderLinesAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>>(this, new { purchaseOrderId }) ?? new List<OpenPurchaseOrderLineDto>();
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Failure(ex.Message);
            }
        }
    }
}
