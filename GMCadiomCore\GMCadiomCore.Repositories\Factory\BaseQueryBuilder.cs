﻿namespace GMCadiomCore.Repositories.Factory
{
    public static class BaseQueryBuilder
    {
        public static List<GlobalFilter> GlobalFilters { get; set; }

        public static IBaseUserModel User { get; set; }

        public class MapColumns
        {
            public string InnerCloumn { get; set; } = null!;
            public string OuterCloumn { get; set; } = null!;
        }

        public class GlobalFilter
        {
            public object Value { get; set; } = null!;
            public string ColumnName { get; set; } = null!;
            public string[] IgnoreTables { get; set; } = null!;
        }

        public enum QueryType
        {
            Select,
            Exist,
            Insert,
            Edit,
            Delete,
            Filter,
            MaxId
        }

        private static string[] _BaseModelColumns;
        private static string[] BaseModelColumns
        {
            get
            {
                if (_BaseModelColumns == null)
                    _BaseModelColumns = typeof(BaseTransactionsModel).GetProperties().CleanColumns(false).ToArray();
                return _BaseModelColumns;
            }
        }

        public static string BuildQuery<T>(QueryType queryType, Expression<Func<T, bool>>? SearchValueBool = null, Expression<Func<T, object>>? SearchValueObject = null)
        {
            StringBuilder queryBuilder = new StringBuilder();

            Type tableType = typeof(T);
            string tableName = tableType.GetTableNameValue();
            string fullTableName = $"[{DataConnection.DBName}].[dbo].[{tableName}]";
            PropertyInfo[] columnsProperties = tableType.GetProperties().GetSimpleTypeProperties().Where(prop => !prop.IsDefined(typeof(KeyAttribute), false)).ToArray();
            PropertyInfo[] keysPropertie = tableType.GetProperties().Where(prop => prop.IsDefined(typeof(KeyAttribute), false)).ToArray();

            switch (queryType)
            {
                case QueryType.Select:
                    {
                        queryBuilder.AppendLine($"SELECT {GetTableColumnsName<T>()} FROM {fullTableName}");
                        queryBuilder.ApplyGlobalFilter(tableType);
                    }
                    break;
                case QueryType.Exist:
                    {
                        queryBuilder.AppendLine(@$"SELECT CASE
                                                   WHEN EXISTS (SELECT 1
                                                                FROM {fullTableName} AS [e]
                                                                WHERE [e].[Id] = @Id)
                                                   THEN CAST(1 AS BIT)
                                                   ELSE CAST(0 AS BIT)
                                                   END");
                        queryBuilder.ApplyGlobalFilter(tableType);
                    }
                    break;
                case QueryType.Insert:
                    {
                        queryBuilder.AppendLine($"INSERT INTO {fullTableName}");
                        queryBuilder.AppendLine($"(");
                        for (int i = 0; i < columnsProperties.Length; i++)
                        {
                            if (
                                columnsProperties[i].Name == nameof(BaseModel.ModifaiedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.ModifaiedAt) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedAt))
                                continue;
                            queryBuilder.AppendLine($"[{columnsProperties[i].Name}]");
                            if (i != columnsProperties.Length - 1)
                                queryBuilder.AppendLine($",");
                        }
                        queryBuilder.AppendLine($")");
                        queryBuilder.AppendLine($"VALUES");
                        queryBuilder.AppendLine($"(");
                        for (int i = 0; i < columnsProperties.Length; i++)
                        {
                            if (
                                columnsProperties[i].Name == nameof(BaseModel.ModifaiedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.ModifaiedAt) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedAt))
                                continue;
                            queryBuilder.AppendLine($"{Helper.Utilities.SetParameterSchema(columnsProperties[i])}");
                            if (i != columnsProperties.Length - 1)
                                queryBuilder.AppendLine($",");
                        }
                        queryBuilder.AppendLine($")");

                        if (tableType.GetProperties().Where(x => x.Name == nameof(BaseModel.CreatedBy) || x.Name == nameof(BaseModel.CreatedAt)).FirstOrDefault() != null)
                        {
                            if (User != null)
                            {
                                queryBuilder.AppendLine($"UPDATE {fullTableName}");
                                queryBuilder.AppendLine($"SET");
                                queryBuilder.AppendLine($"{nameof(BaseModel.CreatedBy)} = {User.Id},");
                                queryBuilder.AppendLine($"{nameof(BaseModel.CreatedAt)} = {Helper.Utilities.SetCleanValue(DateTime.Now)}");
                                queryBuilder.AppendLine($"WHERE");
                                for (int i = 0; i < keysPropertie.Length; i++)
                                {
                                    queryBuilder.AppendLine($"{keysPropertie[i].Name} = {Helper.Utilities.SetParameterSchema(keysPropertie[i])}");
                                    if (i != keysPropertie.Length - 1)
                                        queryBuilder.AppendLine($"AND");
                                }
                            }
                        }
                    }
                    break;
                case QueryType.Edit:
                    {
                        queryBuilder.AppendLine($"UPDATE {fullTableName} SET");
                        for (int i = 0; i < columnsProperties.Length; i++)
                        {
                            if (
                                columnsProperties[i].Name == nameof(BaseModel.CreatedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.CreatedAt) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedBy) ||
                                columnsProperties[i].Name == nameof(BaseModel.DeletedAt))
                                continue;
                            queryBuilder.AppendLine($"{columnsProperties[i].Name} = {Helper.Utilities.SetParameterSchema(columnsProperties[i])}");
                            if (i != columnsProperties.Length - 1)
                                queryBuilder.AppendLine($",");
                        }
                        queryBuilder.AppendLine($"WHERE");
                        for (int i = 0; i < keysPropertie.Length; i++)
                        {
                            queryBuilder.AppendLine($"{keysPropertie[i].Name} = {Helper.Utilities.SetParameterSchema(keysPropertie[i])}");
                            if (i != keysPropertie.Length - 1)
                                queryBuilder.AppendLine($"AND");
                        }

                        if (tableType.GetProperties().Any(x => x.Name == nameof(BaseModel.ModifaiedBy) || x.Name == nameof(BaseModel.ModifaiedAt)))
                        {
                            if (User != null)
                            {
                                queryBuilder.AppendLine($"UPDATE {fullTableName}");
                                queryBuilder.AppendLine($"SET");
                                queryBuilder.AppendLine($"{nameof(BaseModel.ModifaiedBy)} = {User.Id},");
                                queryBuilder.AppendLine($"{nameof(BaseModel.ModifaiedAt)} = {Helper.Utilities.SetCleanValue(DateTime.Now)}");
                                queryBuilder.AppendLine($"WHERE");
                                for (int i = 0; i < keysPropertie.Length; i++)
                                {
                                    queryBuilder.AppendLine($"{keysPropertie[i].Name} = {Helper.Utilities.SetParameterSchema(keysPropertie[i])}");
                                    if (i != keysPropertie.Length - 1)
                                        queryBuilder.AppendLine($"AND");
                                }
                            }
                        }
                    }
                    break;
                case QueryType.Delete:
                    {
                        queryBuilder.AppendLine($"DELETE FROM {fullTableName}");
                        queryBuilder.AppendLine($"WHERE");
                        for (int i = 0; i < keysPropertie.Length; i++)
                        {
                            queryBuilder.AppendLine($"{keysPropertie[i].Name} = {Helper.Utilities.SetParameterSchema(keysPropertie[i])}");
                            if (i != keysPropertie.Length - 1)
                                queryBuilder.AppendLine($"AND");
                        }

                        if (tableType.GetProperties().Any(x => x.Name == nameof(BaseModel.DeletedBy) || x.Name == nameof(BaseModel.DeletedAt)))
                        {
                            if (User != null)
                            {
                                queryBuilder.AppendLine($"UPDATE {fullTableName}");
                                queryBuilder.AppendLine($"SET");
                                queryBuilder.AppendLine($"{nameof(BaseModel.DeletedBy)} = {User.Id},");
                                queryBuilder.AppendLine($"{nameof(BaseModel.DeletedAt)} = {Helper.Utilities.SetCleanValue(DateTime.Now)}");
                                queryBuilder.AppendLine($"WHERE");
                                for (int i = 0; i < keysPropertie.Length; i++)
                                {
                                    queryBuilder.AppendLine($"{keysPropertie[i].Name} = {Helper.Utilities.SetParameterSchema(keysPropertie[i])}");
                                    if (i != keysPropertie.Length - 1)
                                        queryBuilder.AppendLine($"AND");
                                }
                            }
                        }
                    }
                    break;
                case QueryType.Filter:
                    {
                        if (SearchValueBool != null)
                        {
                            string Filter = GetWhereClause(SearchValueBool, fullTableName);

                            queryBuilder.AppendLine($"SELECT * FROM {fullTableName}");
                            queryBuilder.AppendLine($"WHERE {Filter}");
                            queryBuilder.ApplyGlobalFilter(tableType);
                        }
                    }
                    break;
                case QueryType.MaxId:
                    {
                        if (SearchValueObject != null)
                        {
                            var ColumnName = Helper.Utilities.GetCorrectPropertyName(SearchValueObject);

                            queryBuilder.AppendLine($"SELECT MAX(CAST({ColumnName} AS INT)) FROM {fullTableName}");
                            queryBuilder.ApplyGlobalFilter(tableType);
                        }
                    }
                    break;
            }

            return queryBuilder.ToString();
        }

        public static string Build<T>(Dictionary<string, MapColumns> Joins)
        {
            StringBuilder queryBuilder = new StringBuilder();

            Type modelType = typeof(T);
            string tableName = modelType.GetTableNameValue();

            queryBuilder.AppendLine($"SELECT {GetAllColumnsName<T>(Joins)} FROM [{DataConnection.DBName}].[dbo].[{tableName}]");

            foreach (var join in Joins)
            {
                string joinKey = join.Key.Replace("Model", "");
                Type? innerTable = ReflectionExtensions.GetTypeFromAssemblyByName(join.Key);

                queryBuilder.AppendLine($"LEFT OUTER JOIN [{DataConnection.DBName}].[dbo].[{joinKey}] ON [{DataConnection.DBName}].[dbo].[{tableName}].{join.Value.InnerCloumn} = [{DataConnection.DBName}].[dbo].[{joinKey}].{join.Value.OuterCloumn}");

                if (innerTable != null)
                    queryBuilder.ApplyGlobalFilter(modelType, innerTable);
            }

            queryBuilder.ApplyGlobalFilter(modelType);

            return Helper.Utilities.FixODBCReservedKeywords(queryBuilder.ToString());
        }

        private static string GetAllColumnsName<T>(Dictionary<string, MapColumns> Tables)
        {
            string fullQuery = string.Empty;

            string mainTableSelection = GetTableColumnsName<T>();
            string outerTablesSelection = GetTableColumnsName(Tables);

            if (string.IsNullOrEmpty(outerTablesSelection))
                fullQuery = string.Join(",", mainTableSelection);
            else
                fullQuery = string.Join(",", mainTableSelection, outerTablesSelection);

            return fullQuery;
        }

        private static string GetTableColumnsName<T>()
        {
            string fullQuery = string.Empty;
            Type mainTable = typeof(T);
            string[] mainTableColumns = mainTable.GetProperties().GetSimpleTypeProperties().CleanColumns(true).ToArray();
            string mainTableSelection = string.Join(",", mainTableColumns);
            fullQuery = string.Join(",", mainTableSelection);
            return fullQuery;
        }

        private static string GetTableColumnsName(Dictionary<string, MapColumns> Tables)
        {
            string fullQuery = string.Empty;
            BindingList<Type> outerTables = new BindingList<Type>();
            foreach (string table in Tables.Keys)
            {
                var type = ReflectionExtensions.GetTypeFromAssemblyByName(table);
                if (type != null)
                    outerTables.Add(type);
            }
            string[] outerTablesColumns = outerTables.Where(x => x != null).SelectMany(x => x.GetProperties().GetSimpleTypeProperties()).CleanColumns(false).Except(BaseModelColumns).ToArray();
            string outerTablesSelection = string.Join(",", outerTablesColumns);
            fullQuery = string.Join(",", outerTablesSelection);
            return fullQuery;
        }

        private static IEnumerable<string> CleanColumns(this IEnumerable<PropertyInfo> properties, bool isMainTable)
        {
            if (isMainTable)
                return properties.Select(x => string.Join(".", /*"dbo",*/ $"[{x.ReflectedType?.GetTableNameValue()}]", x.Name));
            else
                return properties.Select(x => string.Join(".", /*"dbo",*/ $"[{x.DeclaringType?.GetTableNameValue()}]", x.Name));
        }

        private static IEnumerable<PropertyInfo> GetSimpleTypeProperties(this PropertyInfo[] properties)
        {
            var filteredProperties = properties.Where(p =>
                !IsVirtualProperty(p) &&                    // Exclude virtual properties
                !IsGenericCollection(p) &&                  // Exclude generic collections
                !IsNavigationProperty(p) &&                 // Exclude navigation properties
                !IsNotMappedAttribute(p)                    // Exclude NotMapped attribute
            ).ToArray();

            return filteredProperties;
        }

        private static bool IsVirtualProperty(PropertyInfo property)
        {
            MethodInfo? getMethod = property.GetGetMethod();
            return getMethod != null && getMethod.IsVirtual;
        }

        private static bool IsGenericCollection(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;
            return propertyType.IsGenericType && typeof(IEnumerable).IsAssignableFrom(propertyType);
        }

        private static bool IsNavigationProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;
            return !propertyType.IsPrimitive && !propertyType.IsValueType && propertyType != typeof(string) && propertyType != typeof(byte[]);
        }

        private static bool IsNotMappedAttribute(PropertyInfo property)
        {
            return property.IsDefined(typeof(NotMappedAttribute), false);
        }

        public static string GetWhereClause<T>(Expression<Func<T, bool>> expression, string fullTableName)
        {
            return GetExpressionAsString(expression.Body, fullTableName);
        }

        private static string GetExpressionAsString(Expression expression, string fullTableName)
        {
            var value = "";
            var left = Helper.Utilities.GetLeftNode(expression);
            var equality = Helper.Utilities.GetEquality(expression.NodeType);
            var right = Helper.Utilities.GetRightNode(expression);
            if (left is MemberExpression)
            {
                var leftMem = left as MemberExpression;
                value = string.Format("{0} {1} {2}", fullTableName + "." + leftMem?.Member.Name, equality, "{0}");
            }
            if (right is ConstantExpression)
            {
                var rightConst = right as ConstantExpression;
                if (rightConst != null && rightConst.Value != null)
                    value = string.Format(value, Helper.Utilities.SetCleanValue(rightConst.Value));
            }
            else if (right is MemberExpression)
            {
                try
                {
                    var objectMember = Expression.Convert(right, typeof(object));
                    var getterLambda = Expression.Lambda<Func<object>>(objectMember);
                    var getter = getterLambda.Compile();
                    value = string.Format(value, Helper.Utilities.SetCleanValue(getter()));
                }
                catch
                {
                    try
                    {
                        var rightMem = right as MemberExpression;
                        if (rightMem != null && rightMem.Expression is ConstantExpression rightConst)
                        {
                            var member = rightMem.Member.DeclaringType;
                            var type = rightMem.Member.MemberType;
                            if (member != null)
                            {
                                var val = member.GetField(rightMem.Member.Name)?.GetValue(rightConst.Value);
                                value = string.Format(value, val);
                            }
                        }
                    }
                    catch
                    {

                    }
                }
            }
            else if (right is MethodCallExpression)
            {
                var objectMember = Expression.Convert(right, typeof(object));
                var getterLambda = Expression.Lambda<Func<object>>(objectMember);
                var getter = getterLambda.Compile();
                value = string.Format(value, Helper.Utilities.SetCleanValue(getter()));
            }
            if (value == "")
            {
                var leftVal = GetExpressionAsString(left, fullTableName);
                var rightVal = GetExpressionAsString(right, fullTableName);
                value = string.Format("{0} {1} {2}", leftVal, equality, rightVal);
            }
            return Helper.Utilities.FixODBCReservedKeywords(value);
        }

        private static StringBuilder ApplyGlobalFilter(this StringBuilder stringBuilder, Type modelTable)
        {
            if (GlobalFilters == null)
                return stringBuilder;

            foreach (var globalFilter in GlobalFilters)
            {
                string modelName = modelTable.GetTableNameValue();

                if (globalFilter.IgnoreTables.Contains(modelName))
                    continue;

                if (modelTable.GetProperties().FirstOrDefault(x => x.Name == globalFilter.ColumnName) == null)
                    continue;

                if (globalFilter.Value == null)
                    continue;

                string modelTableName = $"[{DataConnection.DBName}].[dbo].[{modelName}]";

                string keyValue = string.Empty;

                if (globalFilter.Value is IEnumerable keyValueEnumerable)
                {
                    var keyValueList = keyValueEnumerable.Cast<object>().ToList();

                    if (keyValueList.Count > 0)
                        keyValue = $"in ({string.Join(',', keyValueList)})";
                }
                else
                {
                    keyValue = $"= {globalFilter.Value}";
                }

                if (string.IsNullOrEmpty(keyValue))
                    continue;

                if (stringBuilder.ToString().Contains("WHERE"))
                    return stringBuilder.AppendLine($"AND {modelTableName}.{globalFilter.ColumnName} {keyValue}");
                else
                    return stringBuilder.AppendLine($"WHERE {modelTableName}.{globalFilter.ColumnName} {keyValue}");
            }

            return stringBuilder;
        }

        private static StringBuilder ApplyGlobalFilter(this StringBuilder stringBuilder, Type modelTable, Type innerTable)
        {
            foreach (var globalFilter in GlobalFilters)
            {
                string modelTableName = modelTable.GetTableNameValue();
                string innerTableName = innerTable.GetTableNameValue();

                if (globalFilter.IgnoreTables.Contains(modelTableName))
                    continue;

                if (globalFilter.IgnoreTables.Contains(innerTableName))
                    continue;

                if (modelTable.GetProperties().FirstOrDefault(x => x.Name == globalFilter.ColumnName) == null)
                    continue;

                if (innerTable.GetProperties().FirstOrDefault(x => x.Name == globalFilter.ColumnName) == null)
                    continue;

                string modelTableFullName = $"[{DataConnection.DBName}].[dbo].[{modelTableName}]";
                string innerTableFullName = $"[{DataConnection.DBName}].[dbo].[{innerTableName}]";

                stringBuilder.AppendLine($"AND {modelTableName}.{globalFilter.ColumnName} = {innerTableFullName}.{globalFilter.ColumnName}");
            }

            return stringBuilder;
        }
    }
}
