﻿namespace GMCadiomCore.Models.Model
{
    public class BaseTransactionsModel : BaseModel, IBaseTransactionsModel
    {
        [NotAuditable]
        [DisplayName("تم الترحيل"), Browsable(false)]
        public bool? Confirmed { get; set; } = false;
        [NotAuditable]
        [Display<PERSON><PERSON>("تم الترحيل فى"), Browsable(false)]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime? ConfirmedAt { get; set; } = DateTime.Today;
        [NotAuditable]
        [DisplayName("تم الترحيل بواسطه"), Browsable(false)]
        public int? ConfirmedBy { get; set; } = 0;
    }
}
