﻿namespace SimpleBooks.Services.API.DI
{
    public static class InjectServiceLayerServices
    {
        public static IServiceCollection InjectServiceServices(this IServiceCollection services)
        {
            Utilities.Initialize(Assembly.GetExecutingAssembly());

            services.InjectPermissionAndSessionServices();

            services.AddHttpClient<IDataService, ApiService>("SimpleBooks", (servicess, client) =>
            {
                //client.BaseAddress = new Uri("http://api-SimpleBooks-test.runasp.net/api/");
                client.BaseAddress = new Uri("https://localhost:7038/api/");
                string token = servicess.GetTokenString();
                if (!string.IsNullOrEmpty(token))
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(AuthenticationDefaults.AuthenticationScheme, token);
            });

            services.AddHttpContextAccessor();
            services.AddSingleton<IDataService, ApiService>();
            services.AddHostedService<ApiMonitorService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            #region HR
            services.AddScoped<IEmployeeService, EmployeeService>();
            #endregion
            #region Purchases
            services.AddScoped<IVendorService, VendorService>();
            services.AddScoped<IVendorTypeService, VendorTypeService>();
            services.AddScoped<IBillService, BillService>();
            services.AddScoped<IBillReturnService, BillReturnService>();
            services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
            #endregion
            #region Sales
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerTypeService, CustomerTypeService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IInvoiceReturnService, InvoiceReturnService>();
            services.AddScoped<ISalesOrderService, SalesOrderService>();
            #endregion
            #region Tax
            services.AddScoped<ITaxTypeService, TaxTypeService>();
            services.AddScoped<ITaxSubTypeService, TaxSubTypeService>();
            #endregion
            #region Treasury
            services.AddScoped<IBankService, BankService>();
            services.AddScoped<IBankAccountService, BankAccountService>();
            services.AddScoped<IBankTransferTreasuryVoucherService, BankTransferTreasuryVoucherService>();
            services.AddScoped<ICheckClearService, CheckClearService>();
            services.AddScoped<ICheckCollectionService, CheckCollectionService>();
            services.AddScoped<ICheckDepositService, CheckDepositService>();
            services.AddScoped<ICheckRejectService, CheckRejectService>();
            services.AddScoped<ICheckReturnService, CheckReturnService>();
            services.AddScoped<ICheckStatusHistoryService, CheckStatusHistoryService>();
            services.AddScoped<ICheckTreasuryVoucherService, CheckTreasuryVoucherService>();
            services.AddScoped<ICheckVaultService, CheckVaultService>();
            services.AddScoped<ICheckVaultLocationService, CheckVaultLocationService>();
            services.AddScoped<ICashTreasuryVoucherService, CashTreasuryVoucherService>();
            services.AddScoped<IDrawerService, DrawerService>();
            services.AddScoped<IDrawerLocationService, DrawerLocationService>();
            services.AddScoped<IExpensesService, ExpensesService>();
            services.AddScoped<IPaymentTermService, PaymentTermService>();
            services.AddScoped<ITreasuryLineService, TreasuryLineService>();
            #endregion
            #region User
            services.AddScoped<ISettingService, SettingService>();
            services.AddScoped<IUserService, UserService>();
            #endregion
            #region Warehouse
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IProductCategoryService, ProductCategoryService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IProductTaxService, ProductTaxService>();
            services.AddScoped<IProductUnitService, ProductUnitService>();
            services.AddScoped<IStoreService, StoreService>();
            services.AddScoped<IUnitService, UnitService>();
            #endregion

            return services;
        }
    }
}
