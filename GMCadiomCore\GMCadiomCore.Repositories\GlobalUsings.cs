﻿global using GMCadiomCore.Models.Abstractions;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.Model;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Repositories.Factory;
global using GMCadiomCore.Shared.Extensions;
global using GMCadiomCore.Shared.Helper;
global using Microsoft.EntityFrameworkCore.Query;
global using Newtonsoft.Json;
global using Newtonsoft.Json.Linq;
global using System.Collections;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using System.ComponentModel.DataAnnotations.Schema;
global using System.Data;
global using System.Linq.Expressions;
global using System.Reflection;
global using System.Text;
