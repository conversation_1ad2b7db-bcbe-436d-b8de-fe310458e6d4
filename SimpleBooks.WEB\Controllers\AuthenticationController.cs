namespace SimpleBooks.Web.Controllers
{
    [AllowAnonymous]
    public class AuthenticationController : Controller
    {
        private readonly Services.Core.Authentication.IAuthenticationService _loginService;

        public AuthenticationController(Services.Core.Authentication.IAuthenticationService loginService)
        {
            _loginService = loginService;
        }

        [HttpGet]
        public IActionResult Login()
        {
            LoginRequest loginDto = new LoginRequest();
            return View(loginDto);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var result = await _loginService.Login(model);

                result.ThrowIfFailure();

                if (result.Data is not null)
                {
                    var tokenHandler = new JwtSecurityTokenHandler();
                    var jwtToken = tokenHandler.ReadToken(result.Data.Token) as JwtSecurityToken;
                    if (jwtToken != null)
                    {
                        // Save identity in cookie
                        var identity = new ClaimsIdentity(jwtToken.Claims, AuthenticationDefaults.CookieAuthenticationHeader);
                        identity.AddClaim(new Claim("access_token", result.Data.Token)); // Add the token as a claim
                        var authProperties = new AuthenticationProperties
                        {
                            IsPersistent = true,
                            ExpiresUtc = DateTimeOffset.UtcNow.AddHours(1)
                        };

                        await HttpContext.SignInAsync(
                            AuthenticationDefaults.CookieAuthenticationHeader,
                            new ClaimsPrincipal(identity),
                            authProperties);

                        // Redirect to the desired page after login
                        return RedirectToAction("Index", "Home");
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("login", ex.Message);
                return View(model);
            }
            return BadRequest();
        }

        [HttpGet]
        public IActionResult UnAuthorizedIndex()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(AuthenticationDefaults.CookieAuthenticationHeader);
            return RedirectToAction("Login");
        }
    }
}
