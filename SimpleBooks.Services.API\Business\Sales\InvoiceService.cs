﻿namespace SimpleBooks.Services.API.Business.Sales
{
    public class InvoiceService : SimpleBooksBaseService<InvoiceModel, IndexInvoiceViewModel, CreateInvoiceViewModel, UpdateInvoiceViewModel>, IInvoiceService
    {
        private readonly ICustomerService _customerService;
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public InvoiceService(

            IHttpClientFactory httpClientFactory,
            ICustomerService customerService,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(httpClientFactory)
        {
            _customerService = customerService;
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync() => await _customerService.SelectiveCustomerDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();

        [HttpRequestMethod(nameof(GetOpenSalesOrdersByCustomerAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<OpenSalesOrderDto>>> GetOpenSalesOrdersByCustomerAsync(Ulid customerId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<OpenSalesOrderDto>>>(this, new { customerId }) ?? new List<OpenSalesOrderDto>();
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenSalesOrderDto>>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod(nameof(GetOpenSalesOrderLinesAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<OpenSalesOrderLineDto>>> GetOpenSalesOrderLinesAsync(Ulid salesOrderId)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<OpenSalesOrderLineDto>>>(this, new { salesOrderId }) ?? new List<OpenSalesOrderLineDto>();
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenSalesOrderLineDto>>.Failure(ex.Message);
            }
        }
    }
}
