﻿namespace SimpleBooks.Services.API.Business.Purchases
{
    public class VendorService : SimpleBooksBaseService<VendorModel, IndexVendorViewModel, CreateVendorViewModel, UpdateVendorViewModel>, IVendorService
    {
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;

        public VendorService(

            IHttpClientFactory httpClientFactory,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService) : base(httpClientFactory)
        {
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
        }

        [HttpRequestMethod(nameof(SelectiveVendorDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<VendorDto>>>(this) ?? new List<VendorDto>();
            return result;
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectivePaymentTermListAsync() => await _paymentTermService.GetSelectListAsync();
    }
}
