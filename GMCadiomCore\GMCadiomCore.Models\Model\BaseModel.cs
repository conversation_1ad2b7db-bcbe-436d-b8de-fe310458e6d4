﻿namespace GMCadiomCore.Models.Model
{
    public class BaseModel : BaseIdentityModel, IBaseModel
    {
        [NotAuditable]
        [DisplayName("تم الاضافه بواسطه"), Browsable(false)]
        public Ulid? CreatedBy { get; set; }
        [NotAuditable]
        [DisplayName("تم الاضافه فى"), Browsable(false)]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime? CreatedAt { get; set; }
        [NotAuditable]
        [DisplayName("تم الحذف بواسطه"), Browsable(false)]
        public Ulid? DeletedBy { get; set; }
        [NotAuditable]
        [DisplayName("تم الحذف فى"), Browsable(false)]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime? DeletedAt { get; set; }
        [NotAuditable]
        [DisplayName("تم التعديل بواسطه"), Browsable(false)]
        public Ulid? ModifaiedBy { get; set; }
        [NotAuditable]
        [DisplayName("تم التعديل فى"), Browsable(false)]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime? ModifaiedAt { get; set; }
        [DisplayName("فعال"), Browsable(false)]
        public bool IsActive { get; set; } = true;

        public override bool Equals(object? other)
        {
            if (other == null) return false;
            if (ReferenceEquals(this, other)) return true;
            if (ReferenceEquals(this, null)) return false;

            return false;
        }

        public virtual bool Equals(BaseModel other)
        {
            if (other == null) { return false; }
            if (ReferenceEquals(this, other)) { return true; }
            return Id == other.Id;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
