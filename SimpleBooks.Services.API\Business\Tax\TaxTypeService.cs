﻿namespace SimpleBooks.Services.API.Business.Tax
{
    public class TaxTypeService : SimpleBooksBaseService<TaxTypeModel, TaxTypeModel, CreateTaxTypeViewModel, UpdateTaxTypeViewModel>, ITaxTypeService
    {
        public TaxTypeService(IHttpClientFactory httpClientFactory) : base(httpClientFactory)
        {
        }

        [HttpRequestMethod(nameof(SelectiveTaxTypeDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> SelectiveTaxTypeDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TaxTypeDto>>>(this) ?? new List<TaxTypeDto>();
            return result;
        }
    }
}
