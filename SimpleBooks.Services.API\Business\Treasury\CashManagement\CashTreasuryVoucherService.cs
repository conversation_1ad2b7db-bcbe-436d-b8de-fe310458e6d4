﻿namespace SimpleBooks.Services.API.Business.Treasury.CashManagement
{
    public class CashTreasuryVoucherService : SimpleBooksBaseService<CashTreasuryVoucherModel, CashTreasuryVoucherModel, CreateCashTreasuryVoucherViewModel, UpdateCashTreasuryVoucherViewModel>, ICashTreasuryVoucherService
    {
        private readonly IVendorService _vendorService;
        private readonly ICustomerService _customerService;
        private readonly IEmployeeService _employeeService;
        private readonly IDrawerService _drawerService;
        private readonly IDrawerLocationService _drawerLocationService;
        private readonly IExpensesService _expensesService;
        private readonly IBillService _billService;
        private readonly IBillReturnService _billReturnService;
        private readonly IInvoiceService _invoiceService;
        private readonly IInvoiceReturnService _invoiceReturnService;

        public CashTreasuryVoucherService(

            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            ICustomerService customerService,
            IEmployeeService employeeService,
            IDrawerService drawerService,
            IDrawerLocationService drawerLocationService,
            IExpensesService expensesService,
            IBillService billService,
            IBillReturnService billReturnService,
            IInvoiceService invoiceService,
            IInvoiceReturnService invoiceReturnService) : base(httpClientFactory)
        {
            _vendorService = vendorService;
            _customerService = customerService;
            _employeeService = employeeService;
            _drawerService = drawerService;
            _drawerLocationService = drawerLocationService;
            _expensesService = expensesService;
            _billService = billService;
            _billReturnService = billReturnService;
            _invoiceService = invoiceService;
            _invoiceReturnService = invoiceReturnService;
        }

        public async Task<ServiceResult<IEnumerable<BeneficiaryTypeEnumeration>>> SelectiveBeneficiaryTypeListAsync() => await Task.FromResult(BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorListAsync() => await _vendorService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerListAsync() => await _customerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveEmployeeListAsync() => await _employeeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TransactionTypeEnumeration>>> SelectiveTransactionTypeListAsync() => await Task.FromResult(TransactionTypeEnumeration.TreasuryCashTransactionTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveDrawerListAsync() => await _drawerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<DrawerLocationModel>>> SelectiveDrawerLocationListAsync() => await _drawerLocationService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<TreasuryLineTypeEnumeration>>> SelectiveTreasuryLineTypeListAsync() => await Task.FromResult(TreasuryLineTypeEnumeration.TreasuryLineTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveExpenseListAsync() => await _expensesService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillListAsync() => await _billService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillReturnListAsync() => await _billReturnService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceListAsync() => await _invoiceService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceReturnListAsync() => await _invoiceReturnService.GetSelectListAsync();

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecksPaginationList), HttpMethodEnum.GET)]
        public async Task<ServiceResult<PaginationList<CashTreasuryVoucherModel>>> GetAllTreasuryVoucherChecksPaginationList(int pageNumber, int pageSize = 25)
        {
            var result = await _httpClient.SendRequest<ServiceResult<PaginationList<CashTreasuryVoucherModel>>>(this, new { pageNumber, pageSize });
            if (result == null || result.Data == null)
                return ServiceResult<PaginationList<CashTreasuryVoucherModel>>.Failure("No treasury vouchers found.");
            return result;
        }

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<CashTreasuryVoucherModel>>> GetAllTreasuryVoucherChecks(Ulid? checkStatusId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CashTreasuryVoucherModel>>>(this, new { checkStatusId }) ?? new List<CashTreasuryVoucherModel>();
            return result;
        }

        [HttpRequestMethod(nameof(GetTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<CashTreasuryVoucherModel?>> GetTreasuryVoucherChecks(string checkId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<CashTreasuryVoucherModel?>>(this, new { checkId });
            if (result == null || result.Data == null)
                return ServiceResult<CashTreasuryVoucherModel?>.Failure(checkId + " not found.");
            return result;
        }

        [HttpRequestMethod(nameof(SelectiveCashTreasuryVoucherDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveCashTreasuryVoucherDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TreasuryVoucherDto>>>(this) ?? new List<TreasuryVoucherDto>();
            return result;
        }

        [HttpRequestMethod(nameof(GetByIdJsonAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            string result = await _httpClient.SendRequest<ServiceResult<string>>(this, new { id }) ?? string.Empty;
            return result;
        }
    }
}
